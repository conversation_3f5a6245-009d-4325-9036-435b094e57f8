<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NAStool - 路由测试</title>
    
    <!-- CSS -->
    <link href="/static/css/tabler.min.css" rel="stylesheet"/>
    <link href="/static/css/tabler-vendors.min.css" rel="stylesheet"/>
    <link href="/static/css/style.css" rel="stylesheet"/>
    <link href="/static/css/nprogress.css" rel="stylesheet"/>
    
    <style>
        .sidebar {
            width: 280px;
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            background: #fff;
            border-right: 1px solid #e6e7e9;
            overflow-y: auto;
            z-index: 1000;
        }
        
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        .nav-menu {
            padding: 1rem 0;
        }
        
        .nav-menu .nav-link {
            padding: 0.75rem 1.5rem;
            color: #626976;
            text-decoration: none;
            display: flex;
            align-items: center;
            border: none;
            background: none;
        }
        
        .nav-menu .nav-link:hover,
        .nav-menu .nav-link.active {
            background: #f1f3f4;
            color: #206bc4;
        }
        
        .nav-menu .nav-link .nav-link-icon {
            margin-right: 0.75rem;
            width: 1.5rem;
            height: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .sub-menu {
            padding-left: 3rem;
        }
        
        .sub-menu .nav-link {
            padding: 0.5rem 1.5rem;
            font-size: 0.875rem;
        }
        
        #loading_tips {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
        }
        
        #top-sub-navbar {
            display: none;
            background: #fff;
            border-bottom: 1px solid #e6e7e9;
            padding: 0.5rem 1rem;
        }
        
        #top-sub-navbar .nav-link {
            padding: 0.5rem 1rem;
            margin-right: 0.5rem;
            border-radius: 0.375rem;
        }
        
        #top-sub-navbar .nav-link.active {
            background: #206bc4;
            color: #fff;
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header p-3">
            <h3>NAStool</h3>
        </div>
        <nav class="nav-menu" id="nav-menu">
            <!-- 菜单将通过JavaScript动态生成 -->
        </nav>
    </div>
    
    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 子菜单栏 -->
        <div id="top-sub-navbar">
            <ul class="nav nav-tabs">
                <!-- 子菜单将通过JavaScript动态生成 -->
            </ul>
        </div>
        
        <!-- 页面内容 -->
        <div id="page_content" class="p-4">
            <div class="container-xl">
                <div class="page-header">
                    <h1>欢迎使用 NAStool</h1>
                    <p>请从左侧菜单选择功能</p>
                </div>
            </div>
        </div>
        
        <!-- 加载提示 -->
        <div id="loading_tips">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="/static/js/jquery-3.3.1.min.js"></script>
    <script src="/static/js/tabler/tabler.min.js"></script>
    <script src="https://unpkg.com/navigo@8.11.1/lib/navigo.min.js"></script>
    <script src="/static/js/util.js"></script>
    <script>
        // 简化版的NProgress
        window.NProgress = {
            start: function() { console.log('Loading started'); },
            done: function() { console.log('Loading done'); }
        };
        
        // 简化版的fresh_tooltip和init_filetree_element
        window.fresh_tooltip = function() { console.log('Refreshing tooltips'); };
        window.init_filetree_element = function() { console.log('Initializing filetree'); };
    </script>
    <script src="/static/js/app.js"></script>
    
    <script>
        // 生成侧边栏菜单
        function generateSidebarMenu() {
            const navMenu = document.getElementById('nav-menu');
            
            menuData.forEach(menu => {
                // 主菜单项
                const mainItem = document.createElement('a');
                mainItem.className = 'nav-link';
                mainItem.href = `#/${menu.page || (menu.nav ? menu.nav[0].page : 'index')}`;
                mainItem.innerHTML = `
                    <span class="nav-link-icon">${menu.icon}</span>
                    <span class="nav-link-title">${menu.name}</span>
                `;
                
                if (menu.page) {
                    mainItem.onclick = (e) => {
                        e.preventDefault();
                        router.navigate(`/${menu.page}`);
                    };
                }
                
                navMenu.appendChild(mainItem);
                
                // 子菜单项
                if (menu.nav) {
                    const subMenuContainer = document.createElement('div');
                    subMenuContainer.className = 'sub-menu';
                    
                    menu.nav.forEach(subMenu => {
                        const subItem = document.createElement('a');
                        subItem.className = 'nav-link';
                        subItem.href = `#/${subMenu.page}`;
                        subItem.innerHTML = `
                            <span class="nav-link-icon">${subMenu.icon}</span>
                            <span class="nav-link-title">${subMenu.name}</span>
                        `;
                        subItem.onclick = (e) => {
                            e.preventDefault();
                            router.navigate(`/${subMenu.page}`);
                        };
                        
                        subMenuContainer.appendChild(subItem);
                    });
                    
                    navMenu.appendChild(subMenuContainer);
                }
            });
        }
        
        // 重写initializeRoutes函数以包含菜单生成
        const originalInitializeRoutes = window.initializeRoutes;
        window.initializeRoutes = async function() {
            await originalInitializeRoutes();
            generateSidebarMenu();
        };
    </script>
</body>
</html>
