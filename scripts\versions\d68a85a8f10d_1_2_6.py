"""1.2.6

Revision ID: d68a85a8f10d
Revises: 702b7666a634
Create Date: 2023-04-16 14:03:56.871650

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd68a85a8f10d'
down_revision = '702b7666a634'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    try:
        with op.batch_alter_table("CONFIG_SYNC_PATHS") as batch_op:
            batch_op.add_column(sa.Column('COMPATIBILITY', sa.Integer, nullable=True))
    except Exception as e:
        pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
