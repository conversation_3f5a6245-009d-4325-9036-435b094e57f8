<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FastAPI Admin Dashboard</title>
    
    <!-- Tabler CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/css/tabler.min.css">
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .hidden-section {
            display: none;
        }
        .main-content {
            min-height: calc(100vh - 100px);
        }
        .login-container {
            max-width: 400px;
            margin: 100px auto;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .card-statistic {
            transition: transform 0.2s;
        }
        .card-statistic:hover {
            transform: translateY(-5px);
        }
        .navbar-brand {
            font-weight: 700;
        }
        .activity-item {
            border-left: 3px solid;
            padding-left: 15px;
            margin-bottom: 15px;
        }
        .content-loading {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        .menu-divider {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            margin: 10px 0;
        }
    </style>
</head>
<body class="border-top-wide border-primary d-flex flex-column">
    <!-- 登录界面 -->
    <div id="login-section">
        <div class="page page-center">
            <div class="container container-tight py-4">
                <div class="text-center mb-4">
                    <a href="." class="navbar-brand navbar-brand-autodark">
                        <h1 class="text-primary">FastAPI Admin</h1>
                    </a>
                </div>
                <div class="card card-md">
                    <div class="card-body">
                        <h2 class="h2 text-center mb-4">登录到管理系统</h2>
                        <form id="login-form" autocomplete="off">
                            <div class="mb-3">
                                <label class="form-label">用户名</label>
                                <input type="text" id="username" class="form-control" placeholder="请输入用户名" autocomplete="off" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">密码</label>
                                <div class="input-group input-group-flat">
                                    <input type="password" id="password" class="form-control" placeholder="请输入密码" autocomplete="off" required>
                                    <span class="input-group-text">
                                        <a href="#" class="link-secondary" title="显示密码" data-bs-toggle="tooltip" id="toggle-password">
                                            <i class="ti ti-eye"></i>
                                        </a>
                                    </span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-check">
                                    <input type="checkbox" class="form-check-input" id="remember-me">
                                    <span class="form-check-label">记住我</span>
                                </label>
                            </div>
                            <div class="form-footer">
                                <button type="submit" class="btn btn-primary w-100">登录</button>
                            </div>
                        </form>
                        <div class="text-center text-muted mt-3">
                            测试账户: admin/admin123 或 user/user123
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主界面 -->
    <div id="main-section" class="hidden-section">
        <header class="navbar navbar-expand-md navbar-light d-print-none">
            <div class="container-xl">
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <h1 class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
                    FastAPI Admin
                </h1>
                <div class="navbar-nav flex-row order-md-last">
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown">
                            <span class="avatar avatar-sm" style="background-image: url(./static/user.png)"></span>
                            <div class="d-none d-xl-block ps-2">
                                <div id="user-name">管理员</div>
                                <div class="mt-1 small text-muted" id="user-role">管理员</div>
                            </div>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow">
                            <a href="#" class="dropdown-item" data-url="/profile">个人资料</a>
                            <a href="#" class="dropdown-item" data-url="/settings">设置</a>
                            <div class="dropdown-divider"></div>
                            <a href="#" class="dropdown-item" id="logout-btn">退出登录</a>
                        </div>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- 导航菜单 -->
        <div class="navbar-expand-md">
            <div class="collapse navbar-collapse" id="navbar-menu">
                <div class="navbar navbar-light">
                    <div class="container-xl">
                        <ul class="navbar-nav" id="main-menu">
                            <!-- 菜单将通过JS动态加载 -->
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="page-wrapper">
            <div class="container-xl">
                <div class="page-header d-print-none">
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="page-title" id="page-title">仪表板</h2>
                            <div class="text-muted mt-1" id="page-subtitle">系统概览和统计信息</div>
                        </div>
                        <div class="col-auto ms-auto d-print-none">
                            <div class="btn-list" id="page-actions">
                                <!-- 页面操作按钮 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="page-body">
                <div class="container-xl">
                    <div id="content-area">
                        <!-- 内容将通过JS动态加载 -->
                        <div class="content-loading">
                            <div class="spinner-border text-primary" role="status"></div>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <footer class="footer footer-transparent d-print-none">
                <div class="container-xl">
                    <div class="row text-center align-items-center flex-row-reverse">
                        <div class="col-12 col-lg-auto mt-3 mt-lg-0">
                            <p>© 2023 FastAPI Admin Dashboard - 版权所有</p>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <!-- Bootstrap & Tabler JS -->
    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/js/tabler.min.js"></script>
    
    <!-- 自定义JS -->
    <script>
        // 内容加载器映射
        const contentLoaders = {
            '/dashboard': loadDashboard,
            '/products': loadProducts,
            '/products/add': loadProductAdd,
            '/products/categories': loadProductCategories,
            '/orders': loadOrders,
            '/customers': loadCustomers,
            '/invoices': loadInvoices,
            '/reports/sales': loadSalesReport,
            '/reports/customers': loadCustomerReport,
            '/reports/inventory': loadInventoryReport,
            '/settings': loadSettings,
            '/profile': loadProfile,
            '/my-orders': loadMyOrders
        };
        
        // 页面操作映射
        const pageActions = {
            '/products': '<a href="#" class="btn btn-primary" data-url="/products/add"><i class="ti ti-plus me-1"></i>添加产品</a>',
            '/orders': '<a href="#" class="btn btn-primary"><i class="ti ti-file-export me-1"></i>导出订单</a>',
            '/customers': '<a href="#" class="btn btn-primary"><i class="ti ti-user-plus me-1"></i>添加客户</a>'
        };
        
        // 页面标题映射
        const pageTitles = {
            '/dashboard': {'title': '仪表板', 'subtitle': '系统概览和统计信息'},
            '/products': {'title': '产品管理', 'subtitle': '浏览和管理所有产品'},
            '/products/add': {'title': '添加产品', 'subtitle': '向系统中添加新产品'},
            '/products/categories': {'title': '产品分类', 'subtitle': '管理产品分类和目录'},
            '/orders': {'title': '订单管理', 'subtitle': '查看和处理客户订单'},
            '/customers': {'title': '客户管理', 'subtitle': '管理系统客户信息'},
            '/invoices': {'title': '发票管理', 'subtitle': '生成和管理销售发票'},
            '/reports/sales': {'title': '销售报表', 'subtitle': '查看销售数据和趋势'},
            '/reports/customers': {'title': '客户报表', 'subtitle': '分析客户行为和统计'},
            '/reports/inventory': {'title': '库存报表', 'subtitle': '监控库存水平和变化'},
            '/settings': {'title': '系统设置', 'subtitle': '配置系统参数和选项'},
            '/profile': {'title': '个人资料', 'subtitle': '管理您的个人账户信息'},
            '/my-orders': {'title': '我的订单', 'subtitle': '查看您的订单历史'}
        };

        $(document).ready(function() {
            // 检查是否已登录
            checkLoginStatus();
            
            // 登录表单提交
            $('#login-form').on('submit', function(e) {
                e.preventDefault();
                const username = $('#username').val();
                const password = $('#password').val();
                
                $.ajax({
                    url: '/api/login',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({username, password}),
                    success: function(response) {
                        if (response.success) {
                            $('#login-section').hide();
                            $('#main-section').removeClass('hidden-section');
                            $('#user-name').text(response.name);
                            $('#user-role').text(response.role === 'admin' ? '管理员' : '普通用户');
                            loadMenu();
                            loadContent('/dashboard');
                        } else {
                            showAlert('登录失败: ' + response.message, 'danger');
                        }
                    },
                    error: function() {
                        showAlert('登录请求失败', 'danger');
                    }
                });
            });
            
            // 显示/隐藏密码
            $('#toggle-password').on('click', function(e) {
                e.preventDefault();
                const passwordField = $('#password');
                const type = passwordField.attr('type') === 'password' ? 'text' : 'password';
                passwordField.attr('type', type);
                $(this).find('i').toggleClass('ti-eye ti-eye-off');
            });
            
            // 退出登录
            $('#logout-btn').on('click', function(e) {
                e.preventDefault();
                $.post('/api/logout', function() {
                    $('#main-section').addClass('hidden-section');
                    $('#login-section').show();
                    $('#username').val('');
                    $('#password').val('');
                });
            });
            
            // 检查登录状态
            function checkLoginStatus() {
                $.get('/api/menu').done(function() {
                    $('#login-section').hide();
                    $('#main-section').removeClass('hidden-section');
                    loadMenu();
                    loadContent('/dashboard');
                    loadUserInfo();
                }).fail(function() {
                    $('#login-section').show();
                    $('#main-section').addClass('hidden-section');
                });
            }
            
            // 加载用户信息
            function loadUserInfo() {
                $.get('/api/user-info', function(userInfo) {
                    $('#user-name').text(userInfo.name);
                    $('#user-role').text(userInfo.role === 'admin' ? '管理员' : '普通用户');
                });
            }
            
            // 加载菜单
            function loadMenu() {
                $.get('/api/menu', function(menuItems) {
                    const $menu = $('#main-menu');
                    $menu.empty();
                    
                    menuItems.forEach(function(item) {
                        if (item.children && item.children.length > 0) {
                            // 有子菜单的菜单项
                            const $menuItem = $(
                                '<li class="nav-item dropdown">' +
                                '<a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" data-bs-auto-close="outside" role="button">' +
                                '<span class="nav-link-icon d-md-none d-lg-inline-block">' +
                                '<i class="' + item.icon + '"></i>' +
                                '</span>' +
                                '<span class="nav-link-title">' + item.title + '</span>' +
                                '</a>' +
                                '<div class="dropdown-menu">' +
                                '<div class="dropdown-menu-columns">' +
                                '<div class="dropdown-menu-column">' +
                                '</div>' +
                                '</div>' +
                                '</div>' +
                                '</li>'
                            );
                            
                            const $submenu = $menuItem.find('.dropdown-menu-column');
                            
                            item.children.forEach(function(child) {
                                const $submenuItem = $(
                                    '<a class="dropdown-item" href="#" data-url="' + child.url + '">' +
                                    '<i class="' + child.icon + ' me-2"></i>' +
                                    child.title +
                                    '</a>'
                                );
                                
                                $submenuItem.on('click', function(e) {
                                    e.preventDefault();
                                    loadContent(child.url);
                                });
                                
                                $submenu.append($submenuItem);
                            });
                            
                            $menu.append($menuItem);
                        } else {
                            // 普通菜单项
                            const $menuItem = $(
                                '<li class="nav-item">' +
                                '<a class="nav-link" href="#" data-url="' + item.url + '">' +
                                '<span class="nav-link-icon d-md-none d-lg-inline-block">' +
                                '<i class="' + item.icon + '"></i>' +
                                '</span>' +
                                '<span class="nav-link-title">' + item.title + '</span>' +
                                '</a>' +
                                '</li>'
                            );
                            
                            $menuItem.on('click', function(e) {
                                e.preventDefault();
                                loadContent(item.url);
                            });
                            
                            $menu.append($menuItem);
                        }
                    });
                });
            }
            
            // 加载内容
            function loadContent(url) {
                // 显示加载中
                $('#content-area').html('<div class="content-loading"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">加载中...</p></div>');
                
                // 设置页面标题
                if (pageTitles[url]) {
                    $('#page-title').text(pageTitles[url].title);
                    $('#page-subtitle').text(pageTitles[url].subtitle);
                } else {
                    $('#page-title').text('页面');
                    $('#page-subtitle').text('内容加载中');
                }
                
                // 设置页面操作按钮
                if (pageActions[url]) {
                    $('#page-actions').html(pageActions[url]);
                    
                    // 为操作按钮添加点击事件
                    $('#page-actions a').on('click', function(e) {
                        e.preventDefault();
                        const actionUrl = $(this).data('url');
                        if (actionUrl) {
                            loadContent(actionUrl);
                        }
                    });
                } else {
                    $('#page-actions').empty();
                }
                
                // 调用对应的内容加载器
                if (contentLoaders[url]) {
                    contentLoaders[url]();
                } else {
                    // 默认页面
                    $('#content-area').html('<div class="alert alert-info">页面正在开发中...</div>');
                }
            }
            
            // 显示提示消息
            function showAlert(message, type = 'info') {
                const alert = $(
                    '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                    '<div class="d-flex">' +
                    '<div>' + message + '</div>' +
                    '</div>' +
                    '<a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>' +
                    '</div>'
                );
                
                $('#content-area').prepend(alert);
                
                // 5秒后自动关闭
                setTimeout(function() {
                    alert.alert('close');
                }, 5000);
            }
            
            // ==================== 内容加载器函数 ====================
            
            // 加载仪表板
            function loadDashboard() {
                $.get('/api/dashboard', function(data) {
                    let html = '';
                    
                    // 生成统计卡片
                    html += '<div class="row row-deck row-cards">';
                    data.stats.forEach(function(stat) {
                        html += `
                            <div class="col-sm-6 col-lg-3">
                                <div class="card card-statistic">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center">
                                            <div class="subheader">${stat.title}</div>
                                        </div>
                                        <div class="h1 mb-3">${stat.value}</div>
                                        <div class="d-flex mb-2">
                                            <div class="text-${stat.color}">${stat.change} 变化</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    html += '</div>';
                    
                    // 生成图表
                    html += `
                        <div class="card mt-4">
                            <div class="card-header">
                                <h3 class="card-title">销售趋势</h3>
                            </div>
                            <div class="card-body">
                                <div id="chart-sales" style="height: 300px"></div>
                            </div>
                        </div>
                    `;
                    
                    // 生成最近活动列表
                    html += `
                        <div class="card mt-4">
                            <div class="card-header">
                                <h3 class="card-title">最近活动</h3>
                            </div>
                            <div class="list-group list-group-flush">
                    `;
                    
                    data.recent_activity.forEach(function(activity) {
                        html += `
                            <div class="list-group-item">
                                <div class="row align-items-center">
                                    <div class="col-auto"><span class="badge bg-${activity.color}"></span></div>
                                    <div class="col text-truncate">
                                        <div class="d-flex align-items-center">
                                            <i class="${activity.icon} text-${activity.color} me-2"></i>
                                            <div>
                                                <div class="text-reset">${activity.user} ${activity.action}</div>
                                                <div class="text-muted text-truncate mt-n1">${activity.time}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    
                    html += `
                            </div>
                        </div>
                    `;
                    
                    $('#content-area').html(html);
                    
                    // 渲染图表
                    renderSalesChart(data.chart_data);
                }).fail(function() {
                    showAlert('加载仪表板数据失败', 'danger');
                });
            }
            
            // 渲染销售图表
            function renderSalesChart(chartData) {
                const ctx = document.getElementById('chart-sales').getContext('2d');
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: chartData.labels,
                        datasets: chartData.datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    drawBorder: false
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }
            
            // 加载产品列表
            function loadProducts() {
                $.get('/api/products', function(products) {
                    let html = `
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">产品列表</h3>
                            </div>
                            <div class="card-body border-bottom py-3">
                                <div class="d-flex">
                                    <div class="text-muted">
                                        显示
                                        <div class="mx-2 d-inline-block">
                                            <input type="text" class="form-control form-control-sm" value="8" size="3">
                                        </div>
                                        条记录
                                    </div>
                                    <div class="ms-auto text-muted">
                                        <div class="ms-2 d-inline-block">
                                            <input type="text" class="form-control form-control-sm" placeholder="搜索产品">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table card-table table-vcenter text-nowrap datatable">
                                    <thead>
                                        <tr>
                                            <th class="w-1">ID</th>
                                            <th>产品名称</th>
                                            <th>分类</th>
                                            <th>价格</th>
                                            <th>库存</th>
                                            <th>状态</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                    `;
                    
                    products.forEach(function(product) {
                        html += `
                            <tr>
                                <td><span class="text-muted">${product.id}</span></td>
                                <td>${product.name}</td>
                                <td>${product.category}</td>
                                <td>¥${product.price.toLocaleString()}</td>
                                <td>${product.stock}</td>
                                <td><span class="badge ${product.status === '有货' ? 'bg-success' : 'bg-danger'}">${product.status}</span></td>
                                <td class="text-end">
                                    <span class="dropdown">
                                        <button class="btn dropdown-toggle align-text-top" data-bs-boundary="viewport" data-bs-toggle="dropdown">操作</button>
                                        <div class="dropdown-menu dropdown-menu-end">
                                            <a class="dropdown-item" href="#">编辑</a>
                                            <a class="dropdown-item" href="#">复制</a>
                                            <a class="dropdown-item" href="#">删除</a>
                                        </div>
                                    </span>
                                </td>
                            </tr>
                        `;
                    });
                    
                    html += `
                                    </tbody>
                                </table>
                            </div>
                            <div class="card-footer d-flex align-items-center">
                                <p class="m-0 text-muted">显示 <span>1</span> 到 <span>${products.length}</span> 条记录，共 <span>${products.length}</span> 条</p>
                                <ul class="pagination m-0 ms-auto">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">上一页</a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                    <li class="page-item">
                                        <a class="page-link" href="#">下一页</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    `;
                    
                    $('#content-area').html(html);
                }).fail(function() {
                    showAlert('加载产品数据失败', 'danger');
                });
            }
            
            // 加载添加产品表单
            function loadProductAdd() {
                $.get('/api/categories', function(categories) {
                    let categoryOptions = '';
                    categories.forEach(function(category) {
                        categoryOptions += `<option value="${category}">${category}</option>`;
                    });
                    
                    const html = `
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="card-title">添加新产品</h3>
                                    </div>
                                    <div class="card-body">
                                        <form id="product-form">
                                            <div class="form-group mb-3">
                                                <label class="form-label">产品名称</label>
                                                <input type="text" class="form-control" name="name" placeholder="输入产品名称" required>
                                            </div>
                                            <div class="form-group mb-3">
                                                <label class="form-label">产品分类</label>
                                                <select class="form-select" name="category" required>
                                                    <option value="">选择分类</option>
                                                    ${categoryOptions}
                                                </select>
                                            </div>
                                            <div class="form-group mb-3">
                                                <label class="form-label">价格</label>
                                                <input type="number" class="form-control" name="price" placeholder="输入价格" min="0" step="0.01" required>
                                            </div>
                                            <div class="form-group mb-3">
                                                <label class="form-label">库存数量</label>
                                                <input type="number" class="form-control" name="stock" placeholder="输入库存数量" min="0" required>
                                            </div>
                                            <div class="form-footer">
                                                <button type="submit" class="btn btn-primary">保存产品</button>
                                                <button type="reset" class="btn btn-secondary">重置表单</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="card-title">添加产品指南</h3>
                                    </div>
                                    <div class="card-body">
                                        <ol>
                                            <li>填写产品基本信息</li>
                                            <li>选择正确的产品分类</li>
                                            <li>设置合理的价格</li>
                                            <li>输入准确的库存数量</li>
                                            <li>点击保存完成添加</li>
                                        </ol>
                                        <div class="alert alert-info mt-3">
                                            <strong>提示：</strong> 添加产品后，您可以在产品列表中进行查看和编辑。
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    $('#content-area').html(html);
                    
                    // 表单提交处理
                    $('#product-form').on('submit', function(e) {
                        e.preventDefault();
                        
                        const formData = new FormData(this);
                        const data = {
                            name: formData.get('name'),
                            category: formData.get('category'),
                            price: parseFloat(formData.get('price')),
                            stock: parseInt(formData.get('stock'))
                        };
                        
                        $.ajax({
                            url: '/api/products',
                            method: 'POST',
                            data: data,
                            success: function(response) {
                                if (response.success) {
                                    showAlert('产品添加成功！', 'success');
                                    $('#product-form')[0].reset();
                                } else {
                                    showAlert('添加产品失败: ' + response.message, 'danger');
                                }
                            },
                            error: function() {
                                showAlert('添加产品请求失败', 'danger');
                            }
                        });
                    });
                }).fail(function() {
                    showAlert('加载分类数据失败', 'danger');
                });
            }
            
            // 加载产品分类页面
            function loadProductCategories() {
                $.get('/api/categories', function(categories) {
                    let html = `
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="card-title">产品分类</h3>
                                    </div>
                                    <div class="card-body">
                                        <div class="list-group list-group-flush">
                    `;
                    
                    categories.forEach(function(category, index) {
                        html += `
                            <div class="list-group-item">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h4 class="mb-1">${category}</h4>
                                        <p class="text-muted mb-0">${index + 1}个产品在此分类中</p>
                                    </div>
                                    <div class="col-auto">
                                        <div class="btn-list">
                                            <a href="#" class="btn btn-sm btn-primary">编辑</a>
                                            <a href="#" class="btn btn-sm btn-danger">删除</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    
                    html += `
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="card-title">添加新分类</h3>
                                    </div>
                                    <div class="card-body">
                                        <form>
                                            <div class="form-group mb-3">
                                                <label class="form-label">分类名称</label>
                                                <input type="text" class="form-control" placeholder="输入分类名称">
                                            </div>
                                            <div class="form-group mb-3">
                                                <label class="form-label">分类描述</label>
                                                <textarea class="form-control" rows="3" placeholder="输入分类描述"></textarea>
                                            </div>
                                            <div class="form-footer">
                                                <button type="submit" class="btn btn-primary">添加分类</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    $('#content-area').html(html);
                }).fail(function() {
                    showAlert('加载分类数据失败', 'danger');
                });
            }
            
            // 加载订单页面
            function loadOrders() {
                $.get('/api/orders', function(orders) {
                    let html = `
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">订单列表</h3>
                            </div>
                            <div class="card-body border-bottom py-3">
                                <div class="d-flex">
                                    <div class="text-muted">
                                        显示
                                        <div class="mx-2 d-inline-block">
                                            <input type="text" class="form-control form-control-sm" value="8" size="3">
                                        </div>
                                        条记录
                                    </div>
                                    <div class="ms-auto text-muted">
                                        <div class="ms-2 d-inline-block">
                                            <input type="text" class="form-control form-control-sm" placeholder="搜索订单">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table card-table table-vcenter text-nowrap datatable">
                                    <thead>
                                        <tr>
                                            <th class="w-1">订单ID</th>
                                            <th>客户</th>
                                            <th>产品</th>
                                            <th>日期</th>
                                            <th>状态</th>
                                            <th>金额</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                    `;
                    
                    orders.forEach(function(order) {
                        let statusClass = '';
                        switch(order.status) {
                            case '已完成': statusClass = 'bg-success'; break;
                            case '配送中': statusClass = 'bg-primary'; break;
                            case '已付款': statusClass = 'bg-info'; break;
                            case '已取消': statusClass = 'bg-danger'; break;
                            default: statusClass = 'bg-secondary';
                        }
                        
                        html += `
                            <tr>
                                <td><span class="text-muted">${order.id}</span></td>
                                <td>${order.customer}</td>
                                <td>${order.product}</td>
                                <td>${order.date}</td>
                                <td><span class="badge ${statusClass}">${order.status}</span></td>
                                <td>¥${order.amount.toLocaleString()}</td>
                                <td class="text-end">
                                    <span class="dropdown">
                                        <button class="btn dropdown-toggle align-text-top" data-bs-boundary="viewport" data-bs-toggle="dropdown">操作</button>
                                        <div class="dropdown-menu dropdown-menu-end">
                                            <a class="dropdown-item" href="#">查看详情</a>
                                            <a class="dropdown-item" href="#">编辑状态</a>
                                            <a class="dropdown-item" href="#">打印发票</a>
                                            <a class="dropdown-item" href="#">取消订单</a>
                                        </div>
                                    </span>
                                </td>
                            </tr>
                        `;
                    });
                    
                    html += `
                                    </tbody>
                                </table>
                            </div>
                            <div class="card-footer d-flex align-items-center">
                                <p class="m-0 text-muted">显示 <span>1</span> 到 <span>${orders.length}</span> 条记录，共 <span>${orders.length}</span> 条</p>
                                <ul class="pagination m-0 ms-auto">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">上一页</a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                    <li class="page-item">
                                        <a class="page-link" href="#">下一页</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    `;
                    
                    $('#content-area').html(html);
                }).fail(function() {
                    showAlert('加载订单数据失败', 'danger');
                });
            }
            
            // 加载客户页面
            function loadCustomers() {
                $.get('/api/customers', function(customers) {
                    let html = `
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">客户列表</h3>
                            </div>
                            <div class="card-body border-bottom py-3">
                                <div class="d-flex">
                                    <div class="text-muted">
                                        显示
                                        <div class="mx-2 d-inline-block">
                                            <input type="text" class="form-control form-control-sm" value="8" size="3">
                                        </div>
                                        条记录
                                    </div>
                                    <div class="ms-auto text-muted">
                                        <div class="ms-2 d-inline-block">
                                            <input type="text" class="form-control form-control-sm" placeholder="搜索客户">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table card-table table-vcenter text-nowrap datatable">
                                    <thead>
                                        <tr>
                                            <th class="w-1">ID</th>
                                            <th>姓名</th>
                                            <th>邮箱</th>
                                            <th>电话</th>
                                            <th>加入日期</th>
                                            <th>状态</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                    `;
                    
                    customers.forEach(function(customer) {
                        let statusClass = '';
                        switch(customer.status) {
                            case '活跃': statusClass = 'bg-success'; break;
                            case '休眠': statusClass = 'bg-warning'; break;
                            case '流失': statusClass = 'bg-danger'; break;
                            default: statusClass = 'bg-secondary';
                        }
                        
                        html += `
                            <tr>
                                <td><span class="text-muted">${customer.id}</span></td>
                                <td>${customer.name}</td>
                                <td>${customer.email}</td>
                                <td>${customer.phone}</td>
                                <td>${customer.join_date}</td>
                                <td><span class="badge ${statusClass}">${customer.status}</span></td>
                                <td class="text-end">
                                    <span class="dropdown">
                                        <button class="btn dropdown-toggle align-text-top" data-bs-boundary="viewport" data-bs-toggle="dropdown">操作</button>
                                        <div class="dropdown-menu dropdown-menu-end">
                                            <a class="dropdown-item" href="#">查看详情</a>
                                            <a class="dropdown-item" href="#">编辑信息</a>
                                            <a class="dropdown-item" href="#">发送邮件</a>
                                            <a class="dropdown-item" href="#">交易记录</a>
                                        </div>
                                    </span>
                                </td>
                            </tr>
                        `;
                    });
                    
                    html += `
                                    </tbody>
                                </table>
                            </div>
                            <div class="card-footer d-flex align-items-center">
                                <p class="m0 text-muted">显示 <span>1</span> 到 <span>${customers.length}</span> 条记录，共 <span>${customers.length}</span> 条</p>
                                <ul class="pagination m-0 ms-auto">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">上一页</a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                    <li class="page-item">
                                        <a class="page-link" href="#">下一页</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    `;
                    
                    $('#content-area').html(html);
                }).fail(function() {
                    showAlert('加载客户数据失败', 'danger');
                });
            }
            
            // 加载发票页面
            function loadInvoices() {
                const html = `
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">发票管理</h3>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="ti ti-info-circle me-2"></i>
                                发票管理功能正在开发中，即将上线。
                            </div>
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h3 class="card-title">发票统计</h3>
                                            <div class="row">
                                                <div class="col-6">
                                                    <div class="text-muted">已开票</div>
                                                    <div class="h2">245</div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="text-muted">待付款</div>
                                                    <div class="h2">32</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h3 class="card-title">发票状态</h3>
                                            <div class="row">
                                                <div class="col-6">
                                                    <div class="text-muted">已付款</div>
                                                    <div class="h2">213</div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="text-muted">已逾期</div>
                                                    <div class="h2">12</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                $('#content-area').html(html);
            }
            
            // 加载销售报表
            function loadSalesReport() {
                const html = `
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">销售报表</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h3 class="card-title">月度销售趋势</h3>
                                            <div id="chart-sales-trend" style="height: 250px"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h3 class="card-title">产品类别分布</h3>
                                            <div id="chart-categories" style="height: 250px"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card mt-4">
                                <div class="card-body">
                                    <h3 class="card-title">销售明细</h3>
                                    <div class="table-responsive">
                                        <table class="table table-vcenter">
                                            <thead>
                                                <tr>
                                                    <th>产品</th>
                                                    <th>销售量</th>
                                                    <th>销售额</th>
                                                    <th>占比</th>
                                                    <th>趋势</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>智能手机</td>
                                                    <td>245</td>
                                                    <td>¥978,755</td>
                                                    <td>32.5%</td>
                                                    <td><span class="text-green"><i class="ti ti-arrow-up"></i> 12.5%</span></td>
                                                </tr>
                                                <tr>
                                                    <td>笔记本电脑</td>
                                                    <td>142</td>
                                                    <td>¥852,858</td>
                                                    <td>28.3%</td>
                                                    <td><span class="text-green"><i class="ti ti-arrow-up"></i> 8.2%</span></td>
                                                </tr>
                                                <tr>
                                                    <td>平板电脑</td>
                                                    <td>98</td>
                                                    <td>¥293,902</td>
                                                    <td>9.8%</td>
                                                    <td><span class="text-red"><i class="ti ti-arrow-down"></i> 3.4%</span></td>
                                                </tr>
                                                <tr>
                                                    <td>智能手表</td>
                                                    <td>132</td>
                                                    <td>¥171,468</td>
                                                    <td>5.7%</td>
                                                    <td><span class="text-green"><i class="ti ti-arrow-up"></i> 15.7%</span></td>
                                                </tr>
                                                <tr>
                                                    <td>无线耳机</td>
                                                    <td>167</td>
                                                    <td>¥133,133</td>
                                                    <td>4.4%</td>
                                                    <td><span class="text-green"><i class="ti ti-arrow-up"></i> 7.3%</span></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                $('#content-area').html(html);
                
                // 模拟渲染图表
                setTimeout(() => {
                    const salesTrendCtx = document.getElementById('chart-sales-trend').getContext('2d');
                    new Chart(salesTrendCtx, {
                        type: 'line',
                        data: {
                            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                            datasets: [{
                                label: '销售额',
                                data: [12000, 19000, 15000, 25000, 22000, 30000],
                                borderColor: '#206bc4',
                                backgroundColor: 'rgba(32, 107, 196, 0.1)',
                                tension: 0.4,
                                fill: true
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false
                        }
                    });
                    
                    const categoriesCtx = document.getElementById('chart-categories').getContext('2d');
                    new Chart(categoriesCtx, {
                        type: 'doughnut',
                        data: {
                            labels: ['智能手机', '笔记本电脑', '平板电脑', '智能手表', '无线耳机'],
                            datasets: [{
                                data: [32.5, 28.3, 9.8, 5.7, 4.4],
                                backgroundColor: ['#206bc4', '#5eba00', '#f51c2c', '#f59f00', '#683ab7']
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'right'
                                }
                            }
                        }
                    });
                }, 500);
            }
            
            // 加载客户报表
            function loadCustomerReport() {
                const html = `
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">客户分析报表</h3>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="ti ti-info-circle me-2"></i>
                                客户分析报表功能正在开发中。
                            </div>
                        </div>
                    </div>
                `;
                
                $('#content-area').html(html);
            }
            
            // 加载库存报表
            function loadInventoryReport() {
                const html = `
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">库存报表</h3>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="ti ti-info-circle me-2"></i>
                                库存报表功能正在开发中。
                            </div>
                        </div>
                    </div>
                `;
                
                $('#content-area').html(html);
            }
            
            // 加载设置页面
            function loadSettings() {
                const html = `
                    <div class="row">
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">基本设置</h3>
                                </div>
                                <div class="card-body">
                                    <form>
                                        <div class="form-group mb-3">
                                            <label class="form-label">系统名称</label>
                                            <input type="text" class="form-control" value="FastAPI Admin Dashboard">
                                        </div>
                                        <div class="form-group mb-3">
                                            <label class="form-label">系统描述</label>
                                            <textarea class="form-control" rows="3">基于FastAPI的管理系统演示</textarea>
                                        </div>
                                        <div class="form-group mb-3">
                                            <label class="form-label">时区</label>
                                            <select class="form-select">
                                                <option value="+8" selected>UTC+8 (北京时间)</option>
                                                <option value="+0">UTC+0 (伦敦时间)</option>
                                                <option value="-5">UTC-5 (纽约时间)</option>
                                            </select>
                                        </div>
                                        <div class="form-footer">
                                            <button type="submit" class="btn btn-primary">保存更改</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">安全设置</h3>
                                </div>
                                <div class="card-body">
                                    <div class="form-group mb-3">
                                        <label class="form-label">密码策略</label>
                                        <select class="form-control">
                                            <option>简单密码</option>
                                            <option selected>中等复杂度密码</option>
                                            <option>复杂密码</option>
                                        </select>
                                    </div>
                                    <div class="form-group mb-3">
                                        <label class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" checked>
                                            <span class="form-check-label">启用双因素认证</span>
                                        </label>
                                    </div>
                                    <div class="form-group mb-3">
                                        <label class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox">
                                            <span class="form-check-label">强制HTTPS连接</span>
                                        </label>
                                    </div>
                                    <div class="form-group mb-3">
                                        <label class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" checked>
                                            <span class="form-check-label">启用登录失败锁定</span>
                                        </label>
                                    </div>
                                    <div class="form-footer">
                                        <button type="submit" class="btn btn-primary">保存设置</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">通知设置</h3>
                                </div>
                                <div class="card-body">
                                    <div class="form-group mb-3">
                                        <label class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" checked>
                                            <span class="form-check-label">邮件通知</span>
                                        </label>
                                    </div>
                                    <div class="form-group mb-3">
                                        <label class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" checked>
                                            <span class="form-check-label">新订单提醒</span>
                                        </label>
                                    </div>
                                    <div class="form-group mb-3">
                                        <label class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox">
                                            <span class="form-check-label">低库存警告</span>
                                        </label>
                                    </div>
                                    <div class="form-group mb-3">
                                        <label class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" checked>
                                            <span class="form-check-label">系统更新通知</span>
                                        </label>
                                    </div>
                                    <div class="form-footer">
                                        <button type="submit" class="btn btn-primary">保存设置</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                $('#content-area').html(html);
            }
            
            // 加载个人资料页面
            function loadProfile() {
                $.get('/api/user-info', function(userInfo) {
                    const html = `
                        <div class="row">
                            <div class="col-lg-4">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <div class="mb-3">
                                            <span class="avatar avatar-xl" style="background-image: url(./static/user.png)"></span>
                                        </div>
                                        <h3 id="profile-name">${userInfo.name}</h3>
                                        <p class="text-muted" id="profile-role">${userInfo.role === 'admin' ? '管理员' : '普通用户'}</p>
                                        <div class="mt-3">
                                            <span class="badge bg-primary">已验证</span>
                                            <span class="badge bg-secondary">活跃用户</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h3 class="card-title">安全信息</h3>
                                    </div>
                                    <div class="card-body">
                                        <div class="list-group list-group-flush">
                                            <div class="list-group-item">
                                                <div class="row align-items-center">
                                                    <div class="col">最后登录IP</div>
                                                    <div class="col-auto">
                                                        <span class="text-muted">*************</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="list-group-item">
                                                <div class="row align-items-center">
                                                    <div class="col">最后登录时间</div>
                                                    <div class="col-auto">
                                                        <span class="text-muted">2023-05-20 14:30</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="list-group-item">
                                                <div class="row align-items-center">
                                                    <div class="col">账户创建时间</div>
                                                    <div class="col-auto">
                                                        <span class="text-muted">2023-01-15</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="card-title">编辑个人资料</h3>
                                    </div>
                                    <div class="card-body">
                                        <form>
                                            <div class="form-group mb-3 row">
                                                <label class="col-3 col-form-label">用户名</label>
                                                <div class="col">
                                                    <input type="text" class="form-control" value="${userInfo.username}" disabled>
                                                    <small class="form-hint">用户名不可更改</small>
                                                </div>
                                            </div>
                                            <div class="form-group mb-3 row">
                                                <label class="col-3 col-form-label">姓名</label>
                                                <div class="col">
                                                    <input type="text" class="form-control" value="${userInfo.name}">
                                                </div>
                                            </div>
                                            <div class="form-group mb-3 row">
                                                <label class="col-3 col-form-label">邮箱地址</label>
                                                <div class="col">
                                                    <input type="email" class="form-control" value="${userInfo.email}">
                                                </div>
                                            </div>
                                            <div class="form-group mb-3 row">
                                                <label class="col-3 col-form-label">电话号码</label>
                                                <div class="col">
                                                    <input type="tel" class="form-control" value="+86 138 0013 8000">
                                                </div>
                                            </div>
                                            <div class="form-group mb-3 row">
                                                <label class="col-3 col-form-label">个人简介</label>
                                                <div class="col">
                                                    <textarea class="form-control" rows="3">这是一个示例的个人简介文本。</textarea>
                                                </div>
                                            </div>
                                            <div class="form-footer">
                                                <button type="submit" class="btn btn-primary">更新资料</button>
                                                <button type="reset" class="btn btn-secondary">重置</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h3 class="card-title">更改密码</h3>
                                    </div>
                                    <div class="card-body">
                                        <form>
                                            <div class="form-group mb-3 row">
                                                <label class="col-3 col-form-label">当前密码</label>
                                                <div class="col">
                                                    <input type="password" class="form-control">
                                                </div>
                                            </div>
                                            <div class="form-group mb-3 row">
                                                <label class="col-3 col-form-label">新密码</label>
                                                <div class="col">
                                                    <input type="password" class="form-control">
                                                </div>
                                            </div>
                                            <div class="form-group mb-3 row">
                                                <label class="col-3 col-form-label">确认新密码</label>
                                                <div class="col">
                                                    <input type="password" class="form-control">
                                                </div>
                                            </div>
                                            <div class="form-footer">
                                                <button type="submit" class="btn btn-primary">更改密码</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    $('#content-area').html(html);
                }).fail(function() {
                    showAlert('加载用户信息失败', 'danger');
                });
            }
            
            // 加载我的订单页面
            function loadMyOrders() {
                const html = `
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">我的订单</h3>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="ti ti-info-circle me-2"></i>
                                这是普通用户的订单页面，与管理员的订单管理页面不同。
                            </div>
                            <div class="table-responsive">
                                <table class="table table-vcenter">
                                    <thead>
                                        <tr>
                                            <th>订单号</th>
                                            <th>产品</th>
                                            <th>日期</th>
                                            <th>状态</th>
                                            <th>金额</th>
                                            <th class="w-1"></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>ORD-1001</td>
                                            <td>智能手机</td>
                                            <td>2023-05-15</td>
                                            <td><span class="badge bg-success">已完成</span></td>
                                            <td>¥3,999</td>
                                            <td><a href="#">查看</a></td>
                                        </tr>
                                        <tr>
                                            <td>ORD-1003</td>
                                            <td>无线耳机</td>
                                            <td>2023-05-17</td>
                                            <td><span class="badge bg-info">已付款</span></td>
                                            <td>¥799</td>
                                            <td><a href="#">查看</a></td>
                                        </tr>
                                        <tr>
                                            <td>ORD-1005</td>
                                            <td>平板电脑</td>
                                            <td>2023-05-19</td>
                                            <td><span class="badge bg-warning">处理中</span></td>
                                            <td>¥2,999</td>
                                            <td><a href="#">查看</a></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
                
                $('#content-area').html(html);
            }
        });
    </script>
</body>
</html>