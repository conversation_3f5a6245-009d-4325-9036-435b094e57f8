<div class="container-xl">
  <div class="page-header d-print-none">
    <div class="row align-items-center">
      <div class="col">
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="javascript:show_rename_udf_modal()" class="btn btn-primary d-none d-sm-inline-flex">
            <i class="ti ti-transform fs-2"></i>
            自定义识别
          </a>
          <a href="javascript:show_rename_udf_modal()" class="btn btn-primary d-sm-none btn-icon">
            <i class="ti ti-transform fs-2"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- 业务页面代码 -->
<div class="page-body">
  <div class="container-xl">
    <div class="row row-cards">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            <div class="d-flex">
              <div class="text-muted">
                共 {{ TotalCount }} 条记录<span class="d-none d-md-inline-block"> ，搜索：</span>
              </div>
              <div class="ms-auto text-muted">
                <div class="ms-2 d-none d-md-inline-block">
                  <input id="search_word" value="{{ Search }}" type="text" class="form-control form-control-sm"
                         aria-label="搜索">
                </div>
              </div>              
            </div>
            <div class="card-actions">
              <a href="javascript:reidentification_all()" class="btn d-none d-md-inline-flex" title="重新识别">
                <i class="ti ti-refresh fs-2"></i>
                重新识别
              </a>
              <a href="javascript:reidentification_all()" class="btn-action d-md-none" title="重新识别">
                <i class="ti ti-refresh fs-2"></i>
              </a>
              <a href="javascript:delete_checked_unknown()" class="btn text-danger d-none d-md-inline-flex" title="批量删除">
                <i class="ti ti-trash fs-2"></i>
                批量删除
              </a>
              <a href="javascript:delete_checked_unknown()" class="btn-action text-danger d-md-none" title="批量删除">
                <i class="ti ti-trash fs-2"></i>
              </a>
            </div>
          </div>
          <div class="table-responsive {% if TotalCount > 0 %}table-page-body{% endif %}">
            <table class="table table-vcenter card-table table-hover table-striped">
              <thead>
              <tr>
                {% if TotalCount > 0 %}
                  <th class="w-1">
                    <input class="form-check-input m-0 align-middle" type="checkbox" aria-label="全选"
                           onclick="select_SelectALL($(this).prop('checked'), 'unknow_record')">
                  </th>
                {% endif %}
                <th>文件名</th>
                <th class="w-5">转移方式</th>
                <th class="w-5"></th>
              </tr>
              </thead>
              <tbody>
              {% if TotalCount > 0 %}
                {% for Path in Items %}
                  <tr>
                    <td>
                      <input class="form-check-input m-0 align-middle" name="unknow_record" value="{{ Path.id }}" type="checkbox">
                    </td>
                    <td>
                      <a href='javascript:navmenu("mediafile?dir={{ Path.name.replace("\\", "/") }}")'>{{ Path.name }}</a>
                      {% if Path.to %}
                        <div class="text-muted">
                          => {{ Path.to }}
                        </div>
                      {% endif %}
                    </td>
                    <td>
                      <div class="text-muted">
                        {{ Path.sync_mode }}
                      </div>
                    <td>
                      <div class="dropdown">
                        <a href="#" class="btn-action" data-bs-toggle="dropdown" aria-expanded="false">
                          <i class="ti ti-dots-vertical fs-2"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end">
                          <a class="dropdown-item" href='javascript:re_identification("unidentification", ["{{ Path.id }}"])'>
                            重新识别
                          </a>
                          <a class="dropdown-item"
                             href='javascript:show_rename_modal("{{ Path.id }}", "{{ Path.name }}", "{{ Path.rmt_mode }}")'>
                            手动识别
                          </a>
                          <a class="dropdown-item text-danger" href='javascript:del_unknown_path("{{ Path.id }}")'>
                            删除
                          </a>
                        </div>
                      </div>
                    </td>
                  </tr>
                {% endfor %}
              {% else %}
                <tr>
                  <td colspan="3" align="center">没有数据</td>
                </tr>
              {% endif %}
              </tbody>
            </table>
          </div>
          {% if TotalCount > 0 %}
            <div class="card-footer d-flex align-items-center">
              <p class="m-0 text-muted">当前页 <span>{{ Count }}</span> 条</p>
              <ul class="pagination m-0 ms-auto">
                <li class="page-item {% if CurrentPage==1 %} disabled {% endif %}">
                  <a class="page-link" href="javascript:go_pre_page('{{ Search }}', {{ CurrentPage }})" tabindex="-1"
                     aria-disabled="true">
                    <i class="ti ti-chevron-left"></i>
                  </a>
                </li>
                {% for page in PageRange %}
                  <li class="page-item {% if page==CurrentPage %} active {% endif %}">
                    <a class="page-link"
                       href="javascript:navmenu('unidentification?s={{ Search }}&page={{ page }}')">{{ page }}</a>
                  </li>
                {% endfor %}
                <li class="page-item {% if CurrentPage >= TotalPage %} disabled {% endif %}">
                  <a class="page-link"
                     href="{% if CurrentPage < TotalPage %}javascript:go_next_page('{{ Search }}', {{ CurrentPage }}){% else %}javascript:void(0){% endif %}">
                    <i class="ti ti-chevron-right"></i>
                  </a>
                </li>
              </ul>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript">
  // 上一页
  function go_pre_page(search, page) {
    navmenu("unidentification?s=" + search + "&page=" + (page - 1))
  }

  // 下一页
  function go_next_page(search, page) {
    navmenu("unidentification?s=" + search + "&page=" + (page + 1))
  }

  // 搜索按钮
  $('#search_word').bind('keypress', function (event) {
    if (event.keyCode == "13") {
      const keyword = $("#search_word").val();
      navmenu("unidentification?s=" + keyword);
    }
  });

  //显示弹出窗口
  function show_rename_modal(id, path, rmt_mode) {
    show_manual_transfer_modal(1, path, rmt_mode, '', id, '');
  }

  //显示自定义识别窗口
  function show_rename_udf_modal() {
    show_manual_transfer_modal(3, '', '', '', '', '');
  }

  //删除路径
  function del_unknown_path(id) {
    if (id == "") {
      return;
    }
    const cmd = "del_unknown_path";
    const data = {"id": id};
    axios_post(cmd, data, function (ret) {
      window_history_refresh();
    });
  }


  //批量删除
  function delete_checked_unknown() {
    let logids = select_GetSelectedVAL("unknow_record");
    if (logids.length === 0) {
      return;
    }
    show_confirm_modal("是否确认删除所有选中的未识别记录？", function () {
      hide_confirm_modal();
      axios_post("del_unknown_path", {"id": logids}, function (ret) {
        window_history_refresh();
      });
    })
  }

  //批量重新识别
  function reidentification_all() {
    let logids = select_GetSelectedVAL("unknow_record");
    if (logids.length === 0) {
      return;
    }
    re_identification("unidentification", logids);
  }

</script>