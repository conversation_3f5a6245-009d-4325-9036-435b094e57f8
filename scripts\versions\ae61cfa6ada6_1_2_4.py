"""1.2.4

Revision ID: ae61cfa6ada6
Revises: 1f5cc26cdd3d
Create Date: 2023-04-11 10:24:45.522668

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'ae61cfa6ada6'
down_revision = '1f5cc26cdd3d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    try:
        with op.batch_alter_table("DOWNLOAD_HISTORY") as batch_op:
            batch_op.add_column(sa.Column('SE', sa.Text, nullable=True))
            batch_op.add_column(sa.Column('SAVE_PATH', sa.Text, nullable=True))
            batch_op.create_index('ix_DOWNLOAD_HISTORY_SAVE_PATH', ['SAVE_PATH'])
    except Exception as e:
        pass
    # ### end Alembic commands ###


def downgrade() -> None:
    pass
