<div class="container-xl">
  <!-- Page title -->
  <div class="page-header d-print-none">
    <div class="row align-items-center">
      <div class="col">
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="javascript:clear_tmdb_cache()" class="btn btn-danger d-none d-sm-inline-block">
            <i class="ti ti-eraser fs-2"></i>
            清空TMDB缓存
          </a>
          <a href="javascript:clear_tmdb_cache()" class="btn btn-danger d-sm-none btn-icon" title="清空TMDB缓存">
            <i class="ti ti-eraser fs-2"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- 业务页面代码 -->
<div class="page-body">
  <div class="container-xl">
    <div class="row row-cards">
      <div class="col-12">
        <div class="card">
          <div class="card-body border-bottom py-3">
            <div class="d-flex">
              <div class="text-muted">
                共 {{ TotalCount }} 条记录
              </div>
              <div class="ms-auto text-muted">
                搜索:
                <div class="ms-2 d-inline-block">
                  <input id="search_word" value="{{ Search }}" type="text"
                         class="form-control form-control-sm" aria-label="搜索">
                </div>
              </div>
            </div>
          </div>
          <div class="table-responsive {% if TotalCount > 0 %}table-page-mini-body{% endif %}">
            <table class="table table-vcenter card-table table-hover table-striped">
              <thead>
              <tr>
                <th>索引</th>
                <th>标题</th>
                <th>TMDB</th>
                <th></th>
                <th></th>
              </tr>
              </thead>
              <tbody>
              {% if TotalCount > 0 %}
                {% for TmdbCache in TmdbCaches %}
                  <tr>
                    <td>
                      <div class="d-flex py-1 align-items-center">
                        <span class="avatar me-2 text-nowrap">
                          {% if TmdbCache[1].media_type == "电影" %}
                            <i class="ti ti-movie fs-2"></i>
                          {% else %}
                            <i class="ti ti-device-tv fs-2"></i>
                          {% endif %}
                        </span>
                        <div class="flex-fill">
                          <div class="font-weight-medium">{{ TmdbCache[2] }}</div>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div>{{ TmdbCache[1].title }}</div>
                    </td>
                    <td>
                      <div class="text-muted text-nowrap">{{ TmdbCache[1].id }}
                        {% if TmdbCache[1].media_type == "电影" %}
                          <a class="ms-1"
                             href="https://www.themoviedb.org/movie/{{ TmdbCache[1].id }}"
                             target="_blank">
                            <i class="ti ti-link fs-2"></i>
                          </a>
                        {% else %}
                          <a class="ms-1" href="https://www.themoviedb.org/tv/{{ TmdbCache[1].id }}"
                             target="_blank">
                            <i class="ti ti-link fs-2"></i>
                          </a>
                        {% endif %}
                      </div>
                    </td>
                    <td>
                      {% if TmdbCache[1].poster_path %}
                        <img class="rounded shadow-sm w-5"
                            src="https://image.tmdb.org/t/p/w500{{ TmdbCache[1].poster_path }}"
                            onerror="this.src='../static/img/no-image.png'" alt=""
                            style="min-width: 50px"/>
                      {% endif %}
                    </td>
                    <td>
                      <div class="dropdown">
                        <a href="#" class="btn-action" data-bs-toggle="dropdown" aria-expanded="false">
                          <i class="ti ti-dots-vertical fs-2"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end">
                          <a class="dropdown-item"
                             href='javascript:show_modify("{{ TmdbCache[2] }}", "{{ TmdbCache[1].title }}")'>
                            修改
                          </a>
                          <a class="dropdown-item text-danger"
                             href='javascript:manual_delete("{{ TmdbCache[0] }}")'>
                            删除
                          </a>
                        </div>
                      </div>
                    </td>
                  </tr>
                {% endfor %}
              {% else %}
                <tr>
                  <td colspan="5" align="center">没有数据</td>
                </tr>
              {% endif %}
              </tbody>
            </table>
          </div>
          {% if TotalCount > 0 %}
            <div class="card-footer d-flex align-items-center">
              <p class="m-0 text-muted">当前页 <span>{{ Count }}</span> 条</p>
              <ul class="pagination m-0 ms-auto">
                <li class="page-item {% if CurrentPage==1 %} disabled {% endif %}">
                  <a class="page-link" href="javascript:go_pre_page('{{ Search }}', {{ CurrentPage }})" tabindex="-1" aria-disabled="true">
                    <i class="ti ti-chevron-left fs-2"></i>
                  </a>
                </li>
                {% for page in PageRange %}
                  <li class="page-item {% if page==CurrentPage %} active {% endif %}">
                    <a class="page-link"
                       href="javascript:navmenu('tmdbcache?s={{ Search }}&page={{ page }}')">{{ page }}</a>
                  </li>
                {% endfor %}
                <li class="page-item {% if CurrentPage >= TotalPage %} disabled {% endif %}">
                  <a class="page-link"
                     href="{% if CurrentPage < TotalPage %}javascript:go_next_page('{{ Search }}', {{ CurrentPage }}){% else %}javascript:void(0){% endif %}">
                    <i class="ti ti-chevron-right fs-2"></i>
                  </a>
                </li>
              </ul>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
<div class="modal modal-blur fade" id="tmdb_cache_detail" tabindex="-1" role="dialog" aria-hidden="true"
     data-bs-backdrop="static" data-bs-keyboard="false">
  <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="tmdb_cache_title">缓存条目</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <pre class="modal-body" id="tmdb_cache_content"
           style="display:flex; flex-direction:column-reverse;font-size:12px;">
            </pre>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">取消</button>
      </div>
    </div>
  </div>
</div>
<div class="modal modal-blur fade" id="modal-tmdbcache-modify" tabindex="-1" role="dialog" aria-hidden="true"
     data-bs-backdrop="static" data-bs-keyboard="false">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">修改TMDB缓存</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row">
          <label class="form-label required">标题</label>
          <div class="row mb-3">
            <input type="hidden" id="tmdbcache_key">
            <input type="text" value="" id="tmdbcache_title" class="form-control" placeholder="标题">
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-link me-auto" data-bs-dismiss="modal">取消</button>
        <a href="javascript:modify_title()" id="tmdbcache_modify_btn" class="btn btn-primary">确定</a>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript">
  // 上一页
  function go_pre_page(search, page) {
    navmenu("tmdbcache?s=" + search + "&page=" + (page - 1))
  }

  // 下一页
  function go_next_page(search, page) {
    navmenu("tmdbcache?s=" + search + "&page=" + (page + 1))
  }

  // 显示修改
  function show_modify(key, title) {
    $("#tmdbcache_key").val(key);
    $("#tmdbcache_title").val(title);
    $("#modal-tmdbcache-modify").modal('show');
  }

  // 修改标题
  function modify_title() {
    const key = $("#tmdbcache_key").val();
    if (!key) {
      return;
    }
    const title = $("#tmdbcache_title").val();
    if (!title) {
      $("#tmdbcache_title").addClass("is-invalid");
      return;
    } else {
      $("#tmdbcache_title").removeClass("is-invalid");
    }
    $("#modal-tmdbcache-modify").modal('hide');
    axios_post("modify_tmdb_cache", {"key": key, "title": title}, function () {
      window_history_refresh();
    });
  }

  //清空TMDB缓存
  function clear_tmdb_cache() {
    show_confirm_modal("清空TMDB缓存后，将会增加TMDB API的请求次数，增加搜索响应时间，但是可以一次性解决TMDB改名后本地缓存未更新的问题，是否确认？", function () {
      hide_confirm_modal();
      axios_post("clear_tmdb_cache", {}, function (ret) {
        show_success_modal("TMDB缓存清理完成！");
        window_history_refresh();
      });
    });
  }

  //删除提示
  function manual_delete(key) {
    show_confirm_modal(key + " 对应的缓存TMDB条目将被删除，是否确认？", function () {
      hide_confirm_modal();
      axios_post("delete_tmdb_cache", {"cache_key": key}, function () {
        window_history_refresh();
      });
    });
  }

  // 搜索按钮
  $('#search_word').bind('keypress', function (event) {
    if (event.keyCode == "13") {
      let keyword = $("#search_word").val();
      navmenu("tmdbcache?s=" + keyword);
    }
  });

</script>