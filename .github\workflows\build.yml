name: NAStool Docker
on:
  workflow_dispatch:
  push:
    branches:
      - master
    paths:
      - version.py
      - .github/workflows/build.yml
      - package_list.txt
      - requirements.txt
      - docker/Dockerfile

jobs:
  build:
    runs-on: ubuntu-latest
    name: Build Docker Image
    steps:
      -
        name: Checkout
        uses: actions/checkout@master

      -
        name: Release version
        id: release_version
        run: |
          app_version=$(cat version.py |sed -ne "s/APP_VERSION\s=\s'v\(.*\)'/\1/gp")
          echo "app_version=$app_version" >> $GITHUB_ENV

      -
        name: Set Up QEMU
        uses: docker/setup-qemu-action@v2

      -
        name: Set Up Buildx
        uses: docker/setup-buildx-action@v2

      -
        name: Login DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - 
        name: Build Image
        uses: docker/build-push-action@v2
        with:
          context: docker
          file: docker/Dockerfile
          platforms: |
            linux/amd64
            linux/arm64
          push: true
          build-args: |
            BRANCH=master
          tags: |
            ${{ secrets.DOCKER_USERNAME }}/nas-tools:latest
            ${{ secrets.DOCKER_USERNAME }}/nas-tools:${{ env.app_version }}
