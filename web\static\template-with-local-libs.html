<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NAStool - 使用本地库</title>
    
    <!-- 本地CSS库 -->
    <link href="/static/libs/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/libs/tabler/css/tabler.min.css" rel="stylesheet">
    <link href="/static/libs/nprogress.min.css" rel="stylesheet">
    
    <!-- 现有的自定义CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
    
    <style>
        .sidebar {
            width: 280px;
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            background: #fff;
            border-right: 1px solid #e6e7e9;
            overflow-y: auto;
            z-index: 1000;
        }
        
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        .nav-menu {
            padding: 1rem 0;
        }
        
        .nav-menu .nav-link {
            padding: 0.75rem 1.5rem;
            color: #626976;
            text-decoration: none;
            display: flex;
            align-items: center;
            border: none;
            background: none;
        }
        
        .nav-menu .nav-link:hover,
        .nav-menu .nav-link.active {
            background: #f1f3f4;
            color: #206bc4;
        }
        
        .nav-menu .nav-link .nav-link-icon {
            margin-right: 0.75rem;
            width: 1.5rem;
            height: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .sub-menu {
            padding-left: 3rem;
        }
        
        .sub-menu .nav-link {
            padding: 0.5rem 1.5rem;
            font-size: 0.875rem;
        }
        
        #loading_tips {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
        }
        
        #top-sub-navbar {
            display: none;
            background: #fff;
            border-bottom: 1px solid #e6e7e9;
            padding: 0.5rem 1rem;
        }
        
        #top-sub-navbar .nav-link {
            padding: 0.5rem 1rem;
            margin-right: 0.5rem;
            border-radius: 0.375rem;
        }
        
        #top-sub-navbar .nav-link.active {
            background: #206bc4;
            color: #fff;
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header p-3">
            <h3>NAStool</h3>
            <p class="text-muted">使用本地库版本</p>
        </div>
        <nav class="nav-menu" id="nav-menu">
            <!-- 菜单将通过JavaScript动态生成 -->
        </nav>
    </div>
    
    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 子菜单栏 -->
        <div id="top-sub-navbar">
            <ul class="nav nav-tabs">
                <!-- 子菜单将通过JavaScript动态生成 -->
            </ul>
        </div>
        
        <!-- 页面内容 -->
        <div id="page_content" class="p-4">
            <div class="container-xl">
                <div class="page-header">
                    <h1>欢迎使用 NAStool</h1>
                    <p>本页面使用本地下载的Bootstrap和Tabler库</p>
                    
                    <!-- 测试Bootstrap组件 -->
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Bootstrap 测试</h5>
                                    <p class="card-text">这是一个Bootstrap卡片组件</p>
                                    <button class="btn btn-primary" onclick="testBootstrap()">测试按钮</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Tabler 测试</h5>
                                    <p class="card-text">这是一个Tabler样式的卡片</p>
                                    <button class="btn btn-success" onclick="testTabler()">测试Tabler</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">NProgress 测试</h5>
                                    <p class="card-text">测试进度条组件</p>
                                    <button class="btn btn-info" onclick="testNProgress()">测试进度条</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 库信息 -->
                    <div class="mt-4">
                        <h3>已加载的本地库</h3>
                        <ul class="list-group">
                            <li class="list-group-item">✅ Bootstrap 5.3.2 - CSS和JS</li>
                            <li class="list-group-item">✅ Tabler 1.0.0-beta17 - CSS和JS</li>
                            <li class="list-group-item">✅ Navigo 8.11.1 - 路由库</li>
                            <li class="list-group-item">✅ NProgress 0.2.0 - 进度条</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 加载提示 -->
        <div id="loading_tips">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>
    </div>
    
    <!-- 本地JavaScript库 -->
    <script src="/static/js/jquery-3.3.1.min.js"></script>
    <script src="/static/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="/static/libs/tabler/js/tabler.min.js"></script>
    <script src="/static/libs/navigo.min.js"></script>
    <script src="/static/libs/nprogress.min.js"></script>
    
    <!-- 现有的工具脚本 -->
    <script src="/static/js/util.js"></script>
    <script src="/static/js/app.js"></script>
    
    <script>
        // 测试函数
        function testBootstrap() {
            alert('Bootstrap 组件工作正常！');
        }
        
        function testTabler() {
            // 创建一个Tabler模态框
            const modal = new bootstrap.Modal(document.createElement('div'));
            alert('Tabler 样式工作正常！');
        }
        
        function testNProgress() {
            NProgress.start();
            setTimeout(() => {
                NProgress.done();
                alert('NProgress 进度条测试完成！');
            }, 2000);
        }
        
        // 生成测试菜单
        function generateTestMenu() {
            const navMenu = document.getElementById('nav-menu');
            const testMenus = [
                { name: '首页', page: 'index', icon: '<i class="ti ti-home"></i>' },
                { name: '搜索', page: 'search', icon: '<i class="ti ti-search"></i>' },
                { name: '设置', page: 'settings', icon: '<i class="ti ti-settings"></i>' }
            ];
            
            testMenus.forEach(menu => {
                const menuItem = document.createElement('a');
                menuItem.className = 'nav-link';
                menuItem.href = `#/${menu.page}`;
                menuItem.innerHTML = `
                    <span class="nav-link-icon">${menu.icon}</span>
                    <span class="nav-link-title">${menu.name}</span>
                `;
                menuItem.onclick = (e) => {
                    e.preventDefault();
                    if (typeof router !== 'undefined') {
                        router.navigate(`/${menu.page}`);
                    }
                };
                navMenu.appendChild(menuItem);
            });
        }
        
        // 页面加载完成后生成菜单
        document.addEventListener('DOMContentLoaded', function() {
            generateTestMenu();
            console.log('本地库加载完成！');
            console.log('Bootstrap版本:', bootstrap.Tooltip.VERSION || 'Bootstrap已加载');
            console.log('jQuery版本:', $.fn.jquery);
            console.log('Navigo已加载:', typeof Navigo !== 'undefined');
            console.log('NProgress已加载:', typeof NProgress !== 'undefined');
        });
    </script>
</body>
</html>
