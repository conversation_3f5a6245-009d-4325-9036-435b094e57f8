"""1.2.2

Revision ID: 13a58bd5311f
Revises: 69508d1aed24
Create Date: 2023-04-04 08:49:43.453901

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '13a58bd5311f'
down_revision = '69508d1aed24'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # 1.2.2
    try:
        with op.batch_alter_table("RSS_TVS") as batch_op:
            batch_op.add_column(sa.Column('FILTER_INCLUDE', sa.Text, nullable=True))
            batch_op.add_column(sa.Column('FILTER_EXCLUDE', sa.Text, nullable=True))
    except Exception as e:
        pass
    try:
        with op.batch_alter_table("RSS_MOVIES") as batch_op:
            batch_op.add_column(sa.Column('FILTER_INCLUDE', sa.Text, nullable=True))
            batch_op.add_column(sa.Column('FILTER_EXCLUDE', sa.Text, nullable=True))
    except Exception as e:
        pass
    # ### end Alembic commands ###


def downgrade() -> None:
    pass
