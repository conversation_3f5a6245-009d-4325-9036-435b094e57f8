<div class="container-xl">
  <!-- Page title -->
  <div class="page-header d-print-none">
    <div class="row align-items-center">
      <div class="col">
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="javascript:show_add_filtergroup_modal()" class="btn btn-primary d-none d-sm-inline-flex">
            <i class="ti ti-plus fs-2"></i>
            新增
          </a>
          <a href="javascript:show_add_filtergroup_modal()" class="btn btn-primary d-sm-none btn-icon">
            <i class="ti ti-plus fs-2"></i>
          </a>
          <a href="javascript:show_restore_filtergroup_modal()" class="btn btn-twitter d-none d-sm-inline-flex">
            <i class="ti ti-reload fs-2"></i>
            恢复
          </a>
          <a href="javascript:show_restore_filtergroup_modal()" class="btn btn-twitter d-sm-none btn-icon">
            <i class="ti ti-reload fs-2"></i>
          </a>
          <a href="javascript:show_import_filtergroup_modal()" class="btn d-none d-sm-inline-flex">
            <i class="ti ti-transfer-in fs-2"></i>
            导入
          </a>
          <a href="javascript:show_import_filtergroup_modal()" class="btn d-sm-none btn-icon">
            <i class="ti ti-transfer-in fs-2"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- 业务页面代码 -->
{% if Count > 0 %}
  <div class="page-body">
    <div class="container-xl">
      <div class="d-grid gap-3 grid-info-card align-items-start">
        {% for RuleGroup in RuleGroups %}
          <div class="card align-self-start">
            <div class="card-header">
              <h3 class="card-title"><strong>{{ RuleGroup.name }}</strong></h3>
              <div class="card-actions btn-actions">
                <a href="javascript:void(0)" onclick="show_filterrule_detail('{{ RuleGroup.id }}')" class="btn-action ms-1" title="折叠">
                  <i class="ti ti-menu-2 fs-2"></i>
                </a>
                <a href="javascript:share_filtergroup('{{ RuleGroup.id }}')" class="btn-action" title="分享规则组">
                  <i class="ti ti-share fs-2"></i>
                </a>
                <a href="javascript:set_default_filtergroup('{{ RuleGroup.id }}')" class="btn-action"
                   title="设置/取消默认">
                  {% if RuleGroup.default == 'Y' %}
                    <i class="ti ti-star-filled fs-2 text-purple"></i>
                  {% else %}
                    <i class="ti ti-star fs-2"></i>
                  {% endif %}
                </a>
                <a href="javascript:show_add_filterrule_modal('{{ RuleGroup.id }}', '{{ RuleGroup.name }}')"
                   class="btn-action" title="增加规则">
                  <i class="ti ti-plus fs-2"></i>
                </a>
                <a href="javascript:del_filtergroup_modal('{{ RuleGroup.id }}', '{{ RuleGroup.name }}')"
                   class="btn-action" title="删除规则组">
                  <i class="ti ti-x fs-2"></i>
                </a>
              </div>
            </div>
            <div class="card-body p-2" id="div_group{{ RuleGroup.id }}">
              <div class="row row-cards">
                {% if RuleGroup.rules %}
                  {% for Rule in RuleGroup.rules %}
                    <div class="col-12">
                      <a class="card card-link card-link-pop card-borderless"
                         href="javascript:show_modify_filterrule_modal('{{ RuleGroup.id }}', '{{ Rule.id }}', '{{ RuleGroup.name }}')"
                         title="编辑规则">
                        <div class="card-body p-2">
                          <span class="badge bg-purple badge-pill position-absolute" style="top: 10px; left:10px">{{ Rule.pri }}</span>
                          <h3 class="card-title ps-4">{{ Rule.name }}</h3>
                          <div class="w-100">
                            {% if Rule.free_text %}
                              <span class="badge bg-info me-1 mb-1 text-wrap text-start"
                                    title="促销" style='word-break: break-all'>{{ Rule.free_text }}</span>
                            {% endif %}
                            {% for include in Rule.include %}
                              <span class="badge badge-outline text-info me-1 mb-1 text-wrap text-start"
                                    title="包含规则" style='word-break: break-all'>包含: {{ include }}</span>
                            {% endfor %}
                            {% for exclude in Rule.exclude %}
                              <span class="badge badge-outline text-red me-1 mb-1 text-wrap text-start"
                                    title="排除规则" style='word-break: break-all'>排除: {{ exclude }}</span>
                            {% endfor %}
                            {% if Rule.size %}
                              <span class="badge badge-outline text-orange me-1 mb-1 text-wrap text-start"
                                    title="大小限制" style='word-break: break-all'>大小: {{ Rule.size }}</span>
                            {% endif %}
                          </div>
                        </div>
                      </a>
                    </div>
                  {% endfor %}
                {% else %}
                  <empty-content title="没有规则" text='请点击"+"添加规则。'></empty-content>
                {% endif %}
              </div>
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  </div>
{% else %}
   <empty-content title="没有规则" text='没有配置任何规则，请点击“新增规则组“按钮。'></empty-content>
{% endif %}
<div class="modal modal-blur fade" id="modal-add-filterrule" tabindex="-1" role="dialog" aria-hidden="true"
     data-bs-backdrop="static" data-bs-keyboard="false">
  <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="modal-filterrule-title">新增规则</h5>
        <input type="hidden" id="filterrule_id"/>
        <input type="hidden" id="filtergroup_id"/>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" style="overflow-x: hidden">
        <div class="row">
          <div class="col-lg-6">
            <div class="mb-3">
              <label class="form-label required">规则名称</label>
              <input type="text" class="form-control" id="rule_name" placeholder="自定义规则名称">
            </div>
          </div>
          <div class="col-lg-6">
            <div class="mb-3">
              <label class="form-label">优先级 <span class="form-help"
                                                     title="同一个规则组内，规则优先级的数值越小优先级越高"
                                                     data-bs-toggle="tooltip">?</span></label>
              <select class="form-select" id="rule_pri">
                {% for i in range(1, 51) %}
                  {% if i == 1 %}
                    <option value="{{ i }}" selected>{{ i }}</option>
                  {% else %}
                    <option value="{{ i }}">{{ i }}</option>
                  {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-6">
            <div class="mb-3">
              <label class="form-label">包含规则 <span class="form-help"
                                                       title="只有种子标题或副标题中包括这些关键字或匹配正式表达式才会命中；对大小写不敏感，配置为空为不启用该规则；可以配置多行，多行之间是【与】的关系，即多行条件都包含时才会命中，单行内可以用'|'来表示【或】，即包括其一即命中"
                                                       data-bs-toggle="tooltip">?</span></label>
              <textarea class="form-control" id="rule_include" rows="4"
                        placeholder="必须包含的关键字或正则表达式"></textarea>
            </div>
          </div>
          <div class="col-lg-6">
            <div class="mb-3">
              <label class="form-label">排除规则 <span class="form-help"
                                                       title="种子标题或副标题中如果有这些关键字或匹配正式表达式则会被跳过；对大小写不敏感，不能带引号，配置为空为不启用该规则；可以配置多行，多行之间是【与】的关系，即多行条件都不包含时才会跳过，单行内可以用'|'来表示【或】，即包括其一即跳过"
                                                       data-bs-toggle="tooltip">?</span></label>
              <textarea class="form-control" id="rule_exclude" rows="4"
                        placeholder="不能包含的关键字或正则表达式"></textarea>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-6">
            <div class="mb-3">
              <label class="form-label">大小限制(GB) <span class="form-help"
                                                           title="只有满足大小限制的种子文件才会被命中，单位是GB，如果是剧集则是指的单集大小（多集时按集数折算）；不配置则不启用该条件；如只配置1个数字表示小于等于该大小的才会被命中；如配置2个数字(使用英文逗号分隔)表示在中间大小的才会被命中"
                                                           data-bs-toggle="tooltip">?</span></label>
              <input type="text" class="form-control" id="rule_sizelimit" value="" placeholder="限制文件大小，单位GB">
            </div>
          </div>
          <div class="col-lg-6">
            <div class="mb-3">
              <label class="form-label">促销 <span class="form-help"
                                                   title="只有满足促销条件的种子才会被命中，免费包括2X免费"
                                                   data-bs-toggle="tooltip">?</span></label>
              <select class="form-select" id="rule_free">
                <option value="" selected>全部</option>
                <option value="1.0 1.0">普通</option>
                <option value="1.0 0.0">免费</option>
                <option value="2.0 0.0">2X免费</option>
              </select>
            </div>
          </div>
        </div>
        {% for Init_RuleGroup in Init_RuleGroups %}
          {% if Init_RuleGroup.rules %}
            <div class="row mb-3">
              <details>
                <summary class="summary">
                  模板-{{ Init_RuleGroup.name }}
                </summary>
                <div class="form-selectgroup">
                  {% for Rule in Init_RuleGroup.rules %}
                    <label class="form-selectgroup-item">
                      <input type="button" value="{{ Rule | safe }}" class="form-selectgroup-input"
                             onclick="apply_templated(this)">
                      <span class="form-selectgroup-label">{{ Rule.name }}</span>
                    </label>
                  {% endfor %}
                </div>
              </details>
            </div>
          {% endif %}
        {% endfor %}
      </div>
      <div class="modal-footer">
        <button id="filterrule_close_btn" class="btn btn-link">
          取消
        </button>
        <button id="add_or_edit_filterrule_btn" class="btn btn-primary ms-auto" onclick="add_filterrule()">
          新增
        </button>
      </div>
    </div>
  </div>
</div>
<div class="modal modal-blur fade" id="modal-add-filtergroup" tabindex="-1" role="dialog" aria-hidden="true"
     data-bs-backdrop="static" data-bs-keyboard="false">
  <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">新增规则组</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col">
            <div class="mb-3">
              <label class="form-label required">规则组名称 <span class="form-help"
                                                                  title="规则组为规则使用的基本单位，一个规则组中可以包含多条规则，按优先级依次匹配，给规则组设定个名称以便于选择使用"
                                                                  data-bs-toggle="tooltip">?</span></label>
              <input type="text" class="form-control" id="rulegroup_name" placeholder="别名">
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-xl-4">
            <div class="mb-3">
              <label class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="rulegroup_default"
                       onclick="change_over_edition_check(this)">
                <span class="form-check-label">默认 <span class="form-help"
                                                          title="将该规则组设为默认规则，在远程搜索、订阅等未指定规则组的场景下默认使用"
                                                          data-bs-toggle="tooltip">?</span></span>
              </label>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <a class="btn btn-link" data-bs-dismiss="modal">
          取消
        </a>
        <a id="add_rulegroup_btn" class="btn btn-primary ms-auto" href="javascript:add_filtergroup()">
          新增
        </a>
      </div>
    </div>
  </div>
</div>
<div class="modal modal-blur fade" id="modal-restore-filtergroup" tabindex="-1" role="dialog" aria-hidden="true"
     data-bs-backdrop="static" data-bs-keyboard="false">
  <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">恢复初始规则</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="form-selectgroup">
          {% for Init_RuleGroup in Init_RuleGroups %}
            <label class="form-selectgroup-item">
              <input type="checkbox" name="init_rulegroup" value="{{ Init_RuleGroup.id }}"
                     class="form-selectgroup-input">
              <span class="form-selectgroup-label">{{ Init_RuleGroup.name }}</span>
            </label>
          {% endfor %}
        </div>
      </div>
      <div class="modal-footer">
        <a class="btn btn-link" data-bs-dismiss="modal">
          取消
        </a>
        <a id="restore_rulegroup_btn" class="btn btn-primary ms-auto" href="javascript:restore_filtergroup()">
          恢复
        </a>
      </div>
    </div>
  </div>
</div>
<div class="modal modal-blur fade" id="modal-share-filtergroup" tabindex="-1" role="dialog" aria-hidden="true"
     data-bs-backdrop="static" data-bs-keyboard="false">
  <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">规则分享</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <label class="form-label required">规则内容 <span class="form-help"
                                                          title="复制以下内容分享给他人，获得分享后使用规则导入功能导入分享规则"
                                                          data-bs-toggle="tooltip">?</span></label>
        <pre id="share_filtergroup_code" class="text-wrap text-break"></pre>
      </div>
      <div class="modal-footer">
        <a class="btn btn-primary ms-auto" data-bs-dismiss="modal">
          关闭
        </a>
      </div>
    </div>
  </div>
</div>
<div class="modal modal-blur fade" id="modal-import-filtergroup" tabindex="-1" role="dialog" aria-hidden="true"
     data-bs-backdrop="static" data-bs-keyboard="false">
  <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">规则分享</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <label class="form-label required">规则内容</label>
        <textarea class="form-control" id="import_filtergroup_code" rows="10" placeholder="在此处粘贴分享的规则内容">
          </textarea>
      </div>
      <div class="modal-footer">
        <a class="btn btn-primary ms-auto" href="javascript:import_filtergroup()">
          导入
        </a>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript">
  // 打开新增规则表窗口
  function show_add_filtergroup_modal() {
    $("#rulegroup_name").val('');
    $("#modal-add-filtergroup").modal('show');
  }

  // 打开新增规则窗口
  function show_add_filterrule_modal(groupid, groupname) {
    $("#modal-filterrule-title").text("新增规则：" + groupname);
    $("#filtergroup_id").val(groupid);
    $("#filterrule_id").val('');
    $("#rule_name").val('');
    $("#rule_pri").val('1');
    $("#rule_include").val('');
    $("#rule_exclude").val('');
    $("#rule_sizelimit").val('');
    $("#rule_free").val('');
    $("#add_or_edit_filterrule_btn").text("新增");
    $("#filterrule_close_btn").text("取消").unbind('click').click(function () {
      $("#modal-add-filterrule").modal('hide');
    });
    $("#modal-add-filterrule").modal('show');
  }

  // 打开恢复初始规则窗口
  function show_restore_filtergroup_modal() {
    $("#modal-restore-filtergroup").modal('show');
  }

  // 恢复初始规则
  function restore_filtergroup() {
    let init_rulegroups = {{ Init_RuleGroups | safe }};
    let need_init_groupid = select_GetSelectedVAL("init_rulegroup");
    $("#restore_rulegroup_btn").text("恢复中").attr("disabled", true);
    axios_post("restore_filtergroup", {
      "groupids": need_init_groupid,
      "init_rulegroups": init_rulegroups
    }, function (ret) {
      $("#modal-restore-filtergroup").modal('hide');
      window_history_refresh();
    });
  }

  // 设置默认规则组
  function set_default_filtergroup(id) {
    axios_post("set_default_filtergroup", {"id": id}, function (ret) {
      window_history_refresh();
    });
  }

  //修改规则
  function show_modify_filterrule_modal(groupid, ruleid, groupname) {
    $("#modal-filterrule-title").text("编辑规则：" + groupname);
    $("#filterrule_id").val(ruleid);
    $("#filtergroup_id").val(groupid);
    //获取数据
    axios_post("filterrule_detail", {"ruleid": ruleid, "groupid": groupid}, function (ret) {
      if (ret.info) {
        $("#rule_name").val(ret.info.name);
        $("#rule_pri").val(ret.info.pri);
        $("#rule_include").val(ret.info.include);
        $("#rule_exclude").val(ret.info.exclude);
        $("#rule_sizelimit").val(ret.info.size);
        $("#rule_free").val(ret.info.free);
        $("#add_or_edit_filterrule_btn").text("修改");
        $("#filterrule_close_btn").text("删除").unbind('click').click(function () {
          delete_filterrule(ret.info.id);
        });
        $("#modal-add-filterrule").modal('show');
      }
    });
  }

  // 新增规则组
  function add_filtergroup() {
    const name = $("#rulegroup_name").val();
    if (!name) {
      $("#rulegroup_name").addClass("is-invalid");
      return;
    } else {
      $("#rulegroup_name").removeClass("is-invalid");
    }
    let is_default;
    if ($("#rulegroup_default").prop("checked")) {
      is_default = 'Y';
    } else {
      is_default = 'N';
    }
    const params = {"name": name, "default": is_default};
    axios_post("add_filtergroup", params, function (ret) {
      if (ret.code == 0) {
        $("#modal-add-filtergroup").modal('hide');
        window_history_refresh();
      }
    });
  }

  //删除规则组
  function del_filtergroup_modal(id, name) {
    show_confirm_modal("删除规则组后，该组下所有的规则将会同时被删除，是否确认删除 " + name + " ？", function () {
      hide_confirm_modal();
      axios_post("del_filtergroup", {"id": id}, function (ret) {
        window_history_refresh();
      });
    });
  }

  // 新增规则
  function add_filterrule() {
    const rule_id = $("#filterrule_id").val();
    const group_id = $("#filtergroup_id").val();
    const rule_name = $("#rule_name").val();
    const rule_free = $("#rule_free").val();
    if (!rule_name) {
      $("#rule_name").addClass("is-invalid");
      return;
    } else {
      $("#rule_name").removeClass("is-invalid");
    }
    const rule_sizelimit = $("#rule_sizelimit").val();
    if (rule_sizelimit) {
      const reg = /^[0-9,]*$/;
      if (!reg.test(rule_sizelimit)) {
        $("#rule_sizelimit").addClass("is-invalid");
        return;
      } else {
        $("#rule_sizelimit").removeClass("is-invalid");
      }
    }
    const params = {
      rule_id: rule_id,
      group_id: group_id,
      rule_name: rule_name,
      rule_pri: $("#rule_pri").val(),
      rule_include: $("#rule_include").val(),
      rule_exclude: $("#rule_exclude").val(),
      rule_sizelimit: rule_sizelimit,
      rule_free: rule_free
    };
    axios_post("add_filterrule", params, function (ret) {
      if (ret.code == 0) {
        $("#modal-add-filterrule").modal('hide');
        window_history_refresh();
      }
    });
  }

  // 删除规则
  function delete_filterrule(id) {
    const params = {"id": id};
    axios_post("del_filterrule", params, function (ret) {
      $("#modal-add-filterrule").modal('hide');
      window_history_refresh();
    });
  }

  // 应用规则模板
  function apply_templated(item) {
    let rule = item.value.replace(/'/g, "\"");
    rule = rule.replace(/None/g, "\"\"");
    rule = JSON.parse(rule)
    $("#rule_name").val(rule["name"]);
    $("#rule_include").val(rule["include"].toString().replace(/,/g, "\n"));
    $("#rule_exclude").val(rule["exclude"].toString().replace(/,/g, "\n"));
  }

  //分计规则组
  function share_filtergroup(id) {
    axios_post("share_filtergroup", {"id": id}, function (ret) {
      if (ret.code == 0) {
        $("#share_filtergroup_code").text(ret.string);
        $("#modal-share-filtergroup").modal('show');
      } else {
        show_fail_modal(`无法生成分享：${ret.msg}`);
      }
    });
  }

  //显示导入规则组框
  function show_import_filtergroup_modal() {
    $("#import_filtergroup_code").val("");
    $("#modal-import-filtergroup").modal('show');
  }

  //导入编规则组
  function import_filtergroup() {
    const content = $("#import_filtergroup_code").val();
    if (!content) {
      return;
    }
    $("#modal-import-filtergroup").modal('hide');
    axios_post("import_filtergroup", {"content": content}, function (ret) {
      if (ret.code == 0) {
        window_history_refresh();
      } else {
        show_fail_modal(`规则导入失败：${ret.msg}`, function () {
          $("#modal-import-filtergroup").modal('show');
        });
      }
    });
  }
  //展开/折叠
  function show_filterrule_detail(id){
      $(`#div_group${id}`).slideToggle()
  }
</script>
