<style>
  .tab-title>ul {
    font-size: 1.0rem;
    font-weight: var(--tblr-font-weight-bold);
    border-bottom: var(--tblr-nav-tabs-border-width) solid var(--tblr-nav-tabs-border-color);
  }

  div.tab-title>ul>li {
    min-width: 5rem;
    z-index: 2;
  }

  div.tab-title>ul>li>a {
    display: block;
    text-align: center;
  }

  .modal-content {
    border-color: lightgrey;
  }

  .tab-content {
    border-top: var(--tblr-card-border-width) solid var(--tblr-card-border-color);
    margin-top: 1rem;
  }

  .modal-body>details {
    margin: 0 8px 8px 8px;
  }
</style>

<script type="text/javascript" src="../../static/js/jquery.json.js"></script>

<div class="container-xl">
  <!-- Page title -->
  <div class="page-header d-print-none">
    <div class="row align-items-center">
      <div class="col">
        <input type="hidden" id="is_public_site" value="{{ IsPublic }}" />
      </div>
      <!-- Page title actions -->
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="javascript:show_indexer_statistics_modal()" class="btn d-none d-sm-inline-flex">
            <i class="ti ti-chart-pie fs-2"></i>
            索引统计
          </a>
          <a href="javascript:show_indexer_statistics_modal()" class="btn d-sm-none btn-icon" title="索引统计">
            <i class="ti ti-chart-pie fs-2"></i>
          </a>
          <a href="javascript:add_indexer()" class="btn btn-primary d-none d-sm-inline-flex">
            <i class="ti ti-plus fs-2"></i>
            新增站点
          </a>
          <a href="javascript:add_indexer()" class="btn btn-primary d-sm-none btn-icon">
            <i class="ti ti-plus fs-2"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
{% if Indexers %}
<div class="container-xl tab-content">
  <div id="public-indexer-div" class="tab-pane fade active show" role="tabpanel">
    <div class="d-grid gap-3 grid-info-card">
      {% for site in Indexers %}
      <div class="card card-link-pop">
        <div class="card-header">
          <div class="form-switch">
            <input name="indexer_sites" type="checkbox" class="form-check-input" value="{{ site.id }}" {% if
              site.checked %}checked{% endif %}>
          </div>
          <h3 class="card-title">
            <a href="javascript:void(0)" class="ms-1">
              <strong>{{ site.name }}</strong>
            </a>
          </h3>
          <div class="card-actions btn-actions">
            <a href="javascript:edit_site('{{ site.domain }}')" class="btn-action ms-1" title="编辑站点">
              <i class="ti ti-edit fs-2"></i>
            </a>
          </div>
        </div>
        <div class="card-body">
          <dl class="row">
            <dt class="col-3">站点地址:</dt>
            <dd class="col-9"><a href={{ site.domain }} target="_blank">{{ site.domain }}</a></dd>
            <dt class="col-3">资源类型:</dt>
            <dd class="col-9">
              {% for source in site.source_type %}
              {% if source|string in SourceTypes %}
              <span class="badge bg-blue me-2">{{SourceTypes[source|string]}}</span>
              {% else %}
              <span class="badge bg-blue me-2">{{ source }}</span>
              {% endif %}
              {% endfor %}
            </dd>
            <dt class="col-3">搜索方式:</dt>
            <dd class="col-9">
              <span class="badge bg-lime me-2">{{ site.search_type }}</span>
            </dd>
            <dt class="col-3">下载器:</dt>
            {% if site.downloader and site.downloader|string in DownloadSettings %}
            <dd class="col-9"><span class="badge bg-yellow me-2">{{ DownloadSettings[site.downloader|string] }}</span>
            </dd>
            {% else %}
            <dd class="col-9"><span class="badge bg-yellow me-2">默认</span></dd>
            {% endif %}
          </dl>
          <div class="row">
            <div class="col">
              {% if site.render %}
              <span class="badge bg-yellow-lt mb-1 me-1">仿真</span>
              {% endif %}
              {% if site.proxy %}
              <span class="badge bg-lime-lt mb-1 me-1">代理</span>
              {% endif %}
              {% if site.en_expand %}
              <span class="badge bg-cyan-lt mb-1 me-1">扩展</span>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
</div>
{% else %}
{% if IsPublic == 1 %}
<nodata-found title="没有数据" text="没有可以用的公共索引站点"></nodata-found>
{% else %}
<nodata-found title="没有数据" text="没有认证成功的PT站点信息"></nodata-found>
{% endif %}
{% endif %}
<div class="modal modal-blur fade" id="modal-indexer-statistics" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="card modal-content"
      style="border-top-left-radius:var(--tblr-modal-inner-border-radius) !important; border-top-right-radius:var(--tblr-modal-inner-border-radius) !important">
      <div class="card-header"
        style="border-top-left-radius:var(--tblr-modal-inner-border-radius) !important; border-top-right-radius:var(--tblr-modal-inner-border-radius) !important">
        <ul class="nav nav-fill card-header-tabs nav-tabs rounded-3" data-bs-toggle="tabs" role="tablist">
          <li class="nav-item" role="presentation">
            <a href="#tabs-indexer-chart" class="nav-link active" style="justify-content: center" data-bs-toggle="tab"
              aria-selected="true" role="tab">
              图表
            </a>
          </li>
          <li class="nav-item" role="presentation">
            <a href="#tabs-indexer-list" class="nav-link" style="justify-content: center" data-bs-toggle="tab"
              aria-selected="false" role="tab" tabindex="-1">
              详情
            </a>
          </li>
        </ul>
      </div>
      <div class="card-body p-0 mb-3">
        <div class="tab-content">
          <div class="tab-pane fade active show" id="tabs-indexer-chart" role="tabpanel">
            <div id="indexer_chart_content"></div>
          </div>
          <div class="tab-pane fade" id="tabs-indexer-list" role="tabpanel">
            <div id="table-indexer-list" class="table-responsive table-modal-body">
              <table class="table table-vcenter card-table table-hover table-striped">
                <thead>
                  <tr>
                    <th class="flex-fill"><button class="table-sort" data-sort="sort-name">索引</button></th>
                    <th><button class="table-sort" data-sort="sort-total">请求数</button></th>
                    <th><button class="table-sort" data-sort="sort-fail">失败数</button></th>
                    <th><button class="table-sort" data-sort="sort-avg">平均耗时（秒）</button></th>
                  </tr>
                </thead>
                <tbody id="indexer_list_content" class="table-tbody">
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">确定</button>
      </div>
    </div>
  </div>
</div>

<div class="modal modal-blur fade" id="modal-manual-indexer" tabindex="-1" role="dialog" aria-hidden="true"
  data-bs-backdrop="static" data-bs-keyboard="false">
  <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="indexer_modal_title">新增索引站点</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-label required">ID</label>
              <input type="text" value="" id="indexer_id" class="form-control" placeholder="ID">
            </div>
          </div>
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-label required">名称</label>
              <input type="text" value="" id="indexer_name" class="form-control" placeholder="名称">
            </div>
          </div>
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-label required">站点地址 <span class="form-help" title="站点访问地址，站点签到、数据统计、刷流、内置索引器等将使用"
                  data-bs-toggle="tooltip">?</span>
              </label>
              <input type="text" class="form-control" id="indexer_url" value="" placeholder="站点http地址">
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-label required">检索方式 <span class="form-help" title="站点支持根据哪些参数进行资源检索"
                  data-bs-toggle="tooltip">?</span></label>
              <select class="form-select" id="search_type">
                <option value="title">关键字</option>
                <option value="en_name">英文名</option>
                <option value="douban_id">豆瓣id</option>
                <option value="imdb">imdb id</option>
              </select>
            </div>
          </div>
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-label">解析器</label>
              <select class="form-select" id="parser_setting">
                <option value="">默认</option>
                <option value="InterfaceSpider">InterfaceSpider</option>
                <option value="ShadesSpider">ShadesSpider</option>
                <option value="TorrentLeech">TorrentLeech</option>
                <option value="TNodeSpider">TNodeSpider</option>
                <option value="RarBg">RarBg</option>
              </select>
            </div>
          </div>
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-label">下载器</label>
              <select class="form-select" id="indexer_download_setting">
                <option value="0">默认</option>
                {% for Id, Attr in DownloadSettings.items() %}
                <option value="{{ Id }}">{{ Attr }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="indexer_proxy">
                <span class="form-check-label">使用代理服务器 <span class="form-help"
                    title="开启后该站点的访问将使用代理服务器，代理需在基础设置->系统->代理服务器中设置" data-bs-toggle="tooltip">?</span>
                </span>
              </label>
            </div>
          </div>
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="indexer_render">
                <span class="form-check-label">开启浏览器仿真 <span class="form-help"
                    title="开启后可以兼容更多站点，但获取数据耗时会大幅增加；需要拉取含浏览器内核的镜像才能使用" data-bs-toggle="tooltip">?</span></span>
              </label>
            </div>
          </div>
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="en_expand">
                <span class="form-check-label">使用英文名进行扩展搜索</span>
              </label>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-label required">资源类型 <span class="form-help" title="站点可进行哪种类型资源的检索"
                  data-bs-toggle="tooltip">?</span></label>
              <div class="form-selectgroup">
                {% for key, value in SourceTypes.items() %}
                <label class="form-selectgroup-item">
                  <input type="checkbox" name="source_type" value="{{key}}" class="form-selectgroup-input" checked>
                  <span class="form-selectgroup-label">{{value}}</span>
                </label>
                {% endfor %}
              </div>
            </div>
          </div>
        </div>
        <details>
          <summary class="summary">
            搜索配置 <span class="form-help" title="资源搜索配置参数" data-bs-toggle="tooltip">?</span>
          </summary>
          <div class="row mt-2">
            <textarea class="form-control mt-2" id="search_cfg" rows="1" aria-label=""></textarea>
          </div>
        </details>
        <details>
          <summary class="summary">
            种子过滤配置 <span class="form-help" title="种子过滤配置" data-bs-toggle="tooltip">?</span>
          </summary>
          <div class="row mt-2">
            <textarea class="form-control mt-2" id="torrent_cfg" rows="1" aria-label=""></textarea>
          </div>
        </details>
        <details>
          <summary class="summary">
            资源浏览配置 <span class="form-help" title="资源浏览配置参数" data-bs-toggle="tooltip">?</span>
          </summary>
          <div class="row mt-2">
            <textarea class="form-control mt-2" id="browse_cfg" rows="1" aria-label=""></textarea>
          </div>
        </details>
        <details>
          <summary class="summary">
            目录配置 <span class="form-help" title="目录配置参数" data-bs-toggle="tooltip">?</span>
          </summary>
          <div class="row mt-2">
            <textarea class="form-control mt-2" id="category_cfg" rows="1" aria-label=""></textarea>
          </div>
        </details>
      </div>
      <div class="modal-footer">
        <a href="javascript:do_del_indexer()" class="btn btn-link text-red me-auto" id="indexer_delete_btn">
          删除站点
        </a>
        <div class="btn-list">
          <a href="javascript:do_add_indexer()" name="indexer_add_btn" class="btn btn-primary">添加</a>
          <a href="javascript:do_update_indexer()" name="indexer_edit_btn" class="btn btn-primary">确定</a>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">

  var check_sites = {{ CheckSites | tojson }};

  var current_indexer = NaN;

  // 新增站点
  function add_indexer(site_url) {
    $("#indexer_id").val("").attr("readonly", false);
    $("#indexer_name").val("").attr("readonly", false);
    $("#indexer_url").val("")

    $("#indexer_download_setting").val(0);
    $('parser_setting').val("");
    $('search_type').val("title");

    select_SelectALL(true, "source_type")

    $('#search_cfg').val("");
    $('#torrent_cfg').val("");
    $('browse_cfg').val("");
    $('category_cfg').val("");

    $("#indexer_modal_title").text("新增索引站点");
    $("[name='indexer_add_btn']").show();
    $("[name='indexer_edit_btn']").hide();
    $("#indexer_delete_btn").hide();

    $("#modal-manual-indexer").modal("show");
  }

  function do_add_indexer() {

    var search_cfg = $('#search_cfg').val();
    if (!object_json_valid(search_cfg, false)) {
      show_fail_modal('搜索配置不合法，请检查后重试');
      return
    }

    var torrent_cfg = $('#torrent_cfg').val();
    if (!object_json_valid(torrent_cfg, false)) {
      show_fail_modal('种子过滤配置不合法，请检查后重试');
      return
    }

    var browse_cfg = $('#browse_cfg').val();
    if (!object_json_valid(browse_cfg, true)) {
      show_fail_modal('资源浏览配置不合法，请检查后重试');
      return
    }

    var category_cfg = $('#category_cfg').val();
    if (!object_json_valid(category_cfg, true)) {
      show_fail_modal('目录配置不合法，请检查后重试');
      return
    }

    const data = {
      "id": $("#indexer_id").val(),
      "name": $("#indexer_name").val(),
      "domain": $("#indexer_url").val(),
      "proxy": $('#indexer_proxy').prop('checked'),
      "render": $('#indexer_render').prop('checked'),
      "en_expand": $('#en_expand').prop('checked'),
      "downloader": $("#indexer_download_setting").val(),
      "parser": $('#parser_setting').val(),
      "source_type": select_GetSelectedVAL("source_type").join(','),
      "search_type": $('#search_type').val(),
      "search": search_cfg,
      "torrents": torrent_cfg,
      "browse": browse_cfg,
      "category": category_cfg,
      "public": $('#is_public_site').val()
    };

    axios_post("add_indexer", data, function (ret) {
      if (ret.code === 0) {
        $("#modal-manual-indexer").modal("hide");
        show_success_modal("添加索引站点成功!");
        window_history_refresh();
      } else {
        show_fail_modal(`添加索引站点失败：${ret.msg}!`);
      }
    });

  };

  // 编辑站点
  function edit_site(site_url) {

    current_indexer = NaN

    // 根据ID查询详细信息
    axios_post("get_indexer", { "url": site_url }, function (ret) {
      if (ret.data) {
        current_indexer = ret.data

        $("#indexer_id").val(ret.data.id).attr("readonly", true);
        $("#indexer_name").val(ret.data.name).attr("readonly", true);
        $("#indexer_url").val(ret.data.domain);

        // 下拉
        $("#indexer_download_setting").val(ret.data.downloader ?? 0);
        $("#parser_setting").val(ret.data.parser ?? '');
        $('#search_type').val(ret.data.search_type ?? 'title')
        // 多选
        select_SelectPart(ret.data.source_type, "source_type")

        $("#indexer_proxy").prop("checked", ret.data.proxy);
        $("#indexer_render").prop("checked", ret.data.render);
        $("#en_expand").prop("checked", ret.data.en_expand);

        if (ret.data.search) {
          append_json_to_textarea('search_cfg', ret.data.search)
        }

        if (ret.data.torrents) {
          append_json_to_textarea('torrent_cfg', ret.data.torrents)
        }

        if (ret.data.browse) {
          append_json_to_textarea('browse_cfg', ret.data.browse)
        }

        if (ret.data.category) {
          append_json_to_textarea('category_cfg', ret.data.category)
        }

        $("#indexer_modal_title").text("编辑站点");
        $("[name='indexer_add_btn']").hide();
        $("[name='indexer_edit_btn']").show();
        $("#indexer_delete_btn").show();

        $("#modal-manual-indexer").modal("show");
      }
    });
  }

  function do_update_indexer() {

    if (current_indexer == NaN) {
      show_fail_modal('系统异常, 请重新编辑');
      return
    }

    var indexer_id = $("#indexer_id").val();
    if (current_indexer.id != indexer_id) {
      show_fail_modal('站点ID不一致, 请重新编辑');
      return
    }

    var indexer_url = $("#indexer_url").val();
    if (!indexer_url || !isUrl(indexer_url)) {
      show_fail_modal('站点地址无效, 请重新编辑');
      return
    }
    if (indexer_url == current_indexer.domain) {
      indexer_url = NaN
    }

    var search_cfg = $('#search_cfg').val();
    if (!object_json_valid(search_cfg, false)) {
      show_fail_modal('搜索配置不合法，请检查后重试');
      return
    }
    search_cfg = zip_json(search_cfg)
    if (search_cfg == zip_json(current_indexer.search)) {
      search_cfg = NaN
    }

    var torrent_cfg = $('#torrent_cfg').val();
    if (!object_json_valid(torrent_cfg, false)) {
      show_fail_modal('种子过滤配置不合法，请检查后重试');
      return
    }
    torrent_cfg = zip_json(torrent_cfg)
    if (torrent_cfg == zip_json(current_indexer.torrents)) {
      torrent_cfg = NaN
    }

    var browse_cfg = $('#browse_cfg').val();
    if (!object_json_valid(browse_cfg, true)) {
      show_fail_modal('资源浏览配置不合法，请检查后重试');
      return
    }
    browse_cfg = zip_json(browse_cfg)
    if (browse_cfg == zip_json(current_indexer.browse)) {
      browse_cfg = NaN
    }

    var category_cfg = $('#category_cfg').val();
    if (!object_json_valid(category_cfg, true)) {
      show_fail_modal('目录配置不合法，请检查后重试');
      return
    }
    if (zip_json(category_cfg) == zip_json(current_indexer.category)) {
      category_cfg = NaN
    }

    var proxy = $('#indexer_proxy').prop('checked');
    if (proxy == current_indexer.proxy) {
      proxy = NaN
    }

    var render = $('#indexer_render').prop('checked');
    if (render == current_indexer.render) {
      render = NaN
    }
    var en_expand = $('#en_expand').prop('checked');
    if (en_expand == current_indexer.en_expand) {
      en_expand = NaN
    }

    var downloader = $("#indexer_download_setting").val();
    if (downloader == current_indexer.downloader) {
      downloader = NaN
    }
    var parser_setting = $('#parser_setting').val();
    if (parser_setting == current_indexer.parser) {
      parser_setting = NaN
    }

    var source_type = select_GetSelectedVAL("source_type").join(',');
    if (source_type == current_indexer.source_type) {
      source_type = NaN
    }
    var search_type = $('#search_type').val();
    if (search_type == current_indexer.search_type) {
      search_type = NaN
    }

    if (indexer_url == NaN && proxy == NaN && render == NaN && downloader == NaN && en_expand == NaN
      && source_type == NaN && search_type == NaN && search_cfg == NaN && torrent_cfg == NaN
      && browse_cfg == NaN && parser_setting == NaN && category_cfg == NaN) {
      $("#modal-manual-indexer").modal("hide");
      return;
    }

    const data = {
      "id": indexer_id,
      "domain": indexer_url,
      "proxy": proxy,
      "render": render,
      "en_expand": en_expand,
      "downloader": downloader,
      "parser": parser_setting,
      "source_type": source_type,
      "search_type": search_type,
      "search": zip_json(search_cfg),
      "torrents": zip_json(torrent_cfg),
      "browse": zip_json(browse_cfg),
      "category": zip_json(category_cfg)
    };

    axios_post("update_indexer", data, function (ret) {
      if (ret.code === 0) {
        $("#modal-manual-indexer").modal("hide");
        show_success_modal("更新索引站点成功!");
        window_history_refresh();
      } else {
        show_fail_modal(`更新索引站点失败：${ret.msg}!`);
      }
    });

  };

  function do_del_indexer() {

    if (current_indexer == NaN) {
      show_fail_modal('系统异常, 请重新编辑');
      return
    }

    var indexer_id = $("#indexer_id").val();
    if (current_indexer.id != indexer_id) {
      show_fail_modal('站点ID不一致, 请重新编辑');
      return
    }

    const data = {
      "id": indexer_id
    };

    axios_post("delete_indexer", data, function (ret) {
      if (ret.code === 0) {
        $("#modal-manual-indexer").modal("hide");
        window_history_refresh();
      } else {
        show_fail_modal(`索引站点删除失败：${ret.msg}!`);
      }
    });

  };

  // json合法性检查
  function object_json_valid(json_str, allow_empty) {

    if (!json_str || json_str == '') {
      console.log(json_str)
      return allow_empty;
    }

    try {

      json_obj = JSON.parse(json_str);

      if (typeof json_obj == 'object' && json_obj) {
        if (Object.prototype.toString.call(json_obj) === '[object Array]') {
          console.log('对象为数组')
          return false;
        }
        return true;
      } else {
        console.log('对象为不是object')
        return false;
      }
    } catch (e) {
      console.log('对象解析失败：' + e)
      return false;
    }
  }

  // 压缩json字符串
  function zip_json(raw_json) {

    if (!raw_json || raw_json == '') {
      return raw_json;
    }

    // 压缩
    var t = raw_json.replace(/[\r\n]/g, "")
    for (var n = [], o = !1, i = 0, r = t.length; r > i; i++) {
      var a = t.charAt(i);
      o && a === o ? "\\" !== t.charAt(i - 1) && (o = !1) : o || '"' !== a && "'" !== a ? o || " " !== a && "	" !== a || (a = "") : o = a, n.push(a)
    }
    return n.join("");

  }

  // 格式化json文本内容并填充到textarea
  function append_json_to_textarea(elem_id, json_str) {

    if (!json_str) {
      return
    }

    // 格式化
    var lint_json = JSON.parse(json_str);
    // 格式化显示
    var json = JSON.stringify(lint_json, undefined, 4);

    $("#" + elem_id).val(json);

    var match_break_line = json.match(/\n/ig);
    if (match_break_line) {
      var height = Math.min(match_break_line.length * 22 + 15, 500);
      $("#" + elem_id).height(height + 'px');
    }

  }

  // 判断是否为网址
  function isUrl(str) {
    // 根据网址的特征进行判断
    var hasProtocol = str.startsWith("http://") || str.startsWith("https://");
    var hasDomain = str.includes(".");
    return hasProtocol && hasDomain;
  }

  // 显示索引统计
  function show_indexer_statistics_modal() {
    $("#modal-indexer-statistics").modal("show");
  }

  var indexer_chat = undefined;

  function init_indexer_chart() {
    // 请求数据
    axios_post("get_indexer_statistics", {}, function (ret) {
      // 饼图
      if (typeof (indexer_chat) != 'undefined')
        indexer_chat.dispose();
      indexer_chat = echarts.init($('#indexer_chart_content')[0], null, {
        height: 300
      });
      let options = {
        tooltip: {
          trigger: 'item', valueFormatter: value => value + " 秒"
        },
        dataset: {
          source: ret.dataset.filter(item => {
            return (item[1] > 0 || item[1] === 'avg')
          })
        },
        series: [
          {
            type: 'pie',
            encode: {
              itemName: 'indexer',
              value: 'avg'
            },
            label: {
              formatter: function (params) {
                return params.name + ': ' + params.value[params.encode.value[0]] + " 秒";
              }
            }, emptyCircleStyle: {
              color: 'transparent',
              borderColor: '#ddd',
              borderWidth: 1
            }
          }
        ]
      };
      indexer_chat.setOption(options);

      // 列表
      let html = "";
      for (let item of ret.data) {
        html = `${html}
                <tr>
                  <td class="sort-name" data-name="${item.name}">${item.name}</td>
                  <td class="sort-total" data-total="${item.total}">${item.total}</td>
                  <td class="sort-fail" data-fail="${item.fail}">${item.fail}</td>
                  <td class="sort-avg" data-avg="${item.avg}">${item.avg}</td>
                </tr>
                `
      }
      if (html) {
        $("#indexer_list_content").html(html);
      } else {
        $("#indexer_list_content").html(`<tr><td colspan="4"></td></tr>`);
      }

      let tableDataList = new List('table-indexer-list', {
        sortClass: 'table-sort',
        listClass: 'table-tbody',
        valueNames: ['sort-name', 'sort-total', 'sort-fail', 'sort-avg',
          { attr: 'data-name', name: 'sort-name' },
          { attr: 'data-total', name: 'sort-total' },
          { attr: 'data-fail', name: 'sort-fail' },
          { attr: 'data-avg', name: 'sort-avg' }
        ]
      });

    });

  }

  $(document).ready(function () {

    // 加载图表
    $('#modal-indexer-statistics').off('shown.bs.modal').on('shown.bs.modal', function (e) {
      init_indexer_chart();
    });

    // 监听所有具有相同 name 的 checkbox 的状态变化
    $('input[name="indexer_sites"]').change(function () {
      const itemVal = $(this).val();

      if (this.checked) {
          check_sites.push(itemVal)
      } else {
          const indexToRemove = check_sites.indexOf(itemVal);
          if (indexToRemove > -1) {
            check_sites.splice(indexToRemove, 1);
          }
      }

      let params = {
        key: "UserIndexerSites",
        value: check_sites
      }
      axios_post("set_system_config", params);
      
    });

  });


</script>