/**
 * Fuse.js v6.6.2 - Lightweight fuzzy-search (http://fusejs.io)
 *
 * Copyright (c) 2022 Kiro Risk (http://kiro.me)
 * All Rights Reserved. Apache Software License 2.0
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 */
function t(t){return Array.isArray?Array.isArray(t):"[object Array]"===o(t)}function e(t){return"string"==typeof t}function n(t){return"number"==typeof t}function s(t){return!0===t||!1===t||function(t){return i(t)&&null!==t}(t)&&"[object Boolean]"==o(t)}function i(t){return"object"==typeof t}function r(t){return null!=t}function c(t){return!t.trim().length}function o(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":Object.prototype.toString.call(t)}const h=Object.prototype.hasOwnProperty;class a{constructor(t){this._keys=[],this._keyMap={};let e=0;t.forEach((t=>{let n=l(t);e+=n.weight,this._keys.push(n),this._keyMap[n.id]=n,e+=n.weight})),this._keys.forEach((t=>{t.weight/=e}))}get(t){return this._keyMap[t]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function l(n){let s=null,i=null,r=null,c=1,o=null;if(e(n)||t(n))r=n,s=u(n),i=d(n);else{if(!h.call(n,"name"))throw new Error((t=>`Missing ${t} property in key`)("name"));const t=n.name;if(r=t,h.call(n,"weight")&&(c=n.weight,c<=0))throw new Error((t=>`Property 'weight' in key '${t}' must be a positive integer`)(t));s=u(t),i=d(t),o=n.getFn}return{path:s,id:i,weight:c,src:r,getFn:o}}function u(e){return t(e)?e:e.split(".")}function d(e){return t(e)?e.join("."):e}var g={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(t,e)=>t.score===e.score?t.idx<e.idx?-1:1:t.score<e.score?-1:1,includeMatches:!1,findAllMatches:!1,minMatchCharLength:1,location:0,threshold:.6,distance:100,...{useExtendedSearch:!1,getFn:function(i,c){let o=[],h=!1;const a=(i,c,l)=>{if(r(i))if(c[l]){const u=i[c[l]];if(!r(u))return;if(l===c.length-1&&(e(u)||n(u)||s(u)))o.push(function(t){return null==t?"":function(t){if("string"==typeof t)return t;let e=t+"";return"0"==e&&1/t==-1/0?"-0":e}(t)}(u));else if(t(u)){h=!0;for(let t=0,e=u.length;t<e;t+=1)a(u[t],c,l+1)}else c.length&&a(u,c,l+1)}else o.push(i)};return a(i,e(c)?c.split("."):c,0),h?o:o[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1}};const f=/[^ ]+/g;class p{constructor({getFn:t=g.getFn,fieldNormWeight:e=g.fieldNormWeight}={}){this.norm=function(t=1,e=3){const n=new Map,s=Math.pow(10,e);return{get(e){const i=e.match(f).length;if(n.has(i))return n.get(i);const r=1/Math.pow(i,.5*t),c=parseFloat(Math.round(r*s)/s);return n.set(i,c),c},clear(){n.clear()}}}(e,3),this.getFn=t,this.isCreated=!1,this.setIndexRecords()}setSources(t=[]){this.docs=t}setIndexRecords(t=[]){this.records=t}setKeys(t=[]){this.keys=t,this._keysMap={},t.forEach(((t,e)=>{this._keysMap[t.id]=e}))}create(){!this.isCreated&&this.docs.length&&(this.isCreated=!0,e(this.docs[0])?this.docs.forEach(((t,e)=>{this._addString(t,e)})):this.docs.forEach(((t,e)=>{this._addObject(t,e)})),this.norm.clear())}add(t){const n=this.size();e(t)?this._addString(t,n):this._addObject(t,n)}removeAt(t){this.records.splice(t,1);for(let e=t,n=this.size();e<n;e+=1)this.records[e].i-=1}getValueForItemAtKeyId(t,e){return t[this._keysMap[e]]}size(){return this.records.length}_addString(t,e){if(!r(t)||c(t))return;let n={v:t,i:e,n:this.norm.get(t)};this.records.push(n)}_addObject(n,s){let i={i:s,$:{}};this.keys.forEach(((s,o)=>{let h=s.getFn?s.getFn(n):this.getFn(n,s.path);if(r(h))if(t(h)){let n=[];const s=[{nestedArrIndex:-1,value:h}];for(;s.length;){const{nestedArrIndex:i,value:o}=s.pop();if(r(o))if(e(o)&&!c(o)){let t={v:o,i:i,n:this.norm.get(o)};n.push(t)}else t(o)&&o.forEach(((t,e)=>{s.push({nestedArrIndex:e,value:t})}))}i.$[o]=n}else if(e(h)&&!c(h)){let t={v:h,n:this.norm.get(h)};i.$[o]=t}})),this.records.push(i)}toJSON(){return{keys:this.keys,records:this.records}}}function m(t,e,{getFn:n=g.getFn,fieldNormWeight:s=g.fieldNormWeight}={}){const i=new p({getFn:n,fieldNormWeight:s});return i.setKeys(t.map(l)),i.setSources(e),i.create(),i}function M(t,{errors:e=0,currentLocation:n=0,expectedLocation:s=0,distance:i=g.distance,ignoreLocation:r=g.ignoreLocation}={}){const c=e/t.length;if(r)return c;const o=Math.abs(s-n);return i?c+o/i:o?1:c}function x(t,e,n,{location:s=g.location,distance:i=g.distance,threshold:r=g.threshold,findAllMatches:c=g.findAllMatches,minMatchCharLength:o=g.minMatchCharLength,includeMatches:h=g.includeMatches,ignoreLocation:a=g.ignoreLocation}={}){if(e.length>32)throw new Error(`Pattern length exceeds max of ${32}.`);const l=e.length,u=t.length,d=Math.max(0,Math.min(s,u));let f=r,p=d;const m=o>1||h,x=m?Array(u):[];let y;for(;(y=t.indexOf(e,p))>-1;){let t=M(e,{currentLocation:y,expectedLocation:d,distance:i,ignoreLocation:a});if(f=Math.min(t,f),p=y+l,m){let t=0;for(;t<l;)x[y+t]=1,t+=1}}p=-1;let L=[],k=1,_=l+u;const v=1<<l-1;for(let s=0;s<l;s+=1){let r=0,o=_;for(;r<o;){M(e,{errors:s,currentLocation:d+o,expectedLocation:d,distance:i,ignoreLocation:a})<=f?r=o:_=o,o=Math.floor((_-r)/2+r)}_=o;let h=Math.max(1,d-o+1),g=c?u:Math.min(d+o,u)+l,y=Array(g+2);y[g+1]=(1<<s)-1;for(let r=g;r>=h;r-=1){let c=r-1,o=n[t.charAt(c)];if(m&&(x[c]=+!!o),y[r]=(y[r+1]<<1|1)&o,s&&(y[r]|=(L[r+1]|L[r])<<1|1|L[r+1]),y[r]&v&&(k=M(e,{errors:s,currentLocation:c,expectedLocation:d,distance:i,ignoreLocation:a}),k<=f)){if(f=k,p=c,p<=d)break;h=Math.max(1,2*d-p)}}if(M(e,{errors:s+1,currentLocation:d,expectedLocation:d,distance:i,ignoreLocation:a})>f)break;L=y}const S={isMatch:p>=0,score:Math.max(.001,k)};if(m){const t=function(t=[],e=g.minMatchCharLength){let n=[],s=-1,i=-1,r=0;for(let c=t.length;r<c;r+=1){let c=t[r];c&&-1===s?s=r:c||-1===s||(i=r-1,i-s+1>=e&&n.push([s,i]),s=-1)}return t[r-1]&&r-s>=e&&n.push([s,r-1]),n}(x,o);t.length?h&&(S.indices=t):S.isMatch=!1}return S}function y(t){let e={};for(let n=0,s=t.length;n<s;n+=1){const i=t.charAt(n);e[i]=(e[i]||0)|1<<s-n-1}return e}class L{constructor(t,{location:e=g.location,threshold:n=g.threshold,distance:s=g.distance,includeMatches:i=g.includeMatches,findAllMatches:r=g.findAllMatches,minMatchCharLength:c=g.minMatchCharLength,isCaseSensitive:o=g.isCaseSensitive,ignoreLocation:h=g.ignoreLocation}={}){if(this.options={location:e,threshold:n,distance:s,includeMatches:i,findAllMatches:r,minMatchCharLength:c,isCaseSensitive:o,ignoreLocation:h},this.pattern=o?t:t.toLowerCase(),this.chunks=[],!this.pattern.length)return;const a=(t,e)=>{this.chunks.push({pattern:t,alphabet:y(t),startIndex:e})},l=this.pattern.length;if(l>32){let t=0;const e=l%32,n=l-e;for(;t<n;)a(this.pattern.substr(t,32),t),t+=32;if(e){const t=l-32;a(this.pattern.substr(t),t)}}else a(this.pattern,0)}searchIn(t){const{isCaseSensitive:e,includeMatches:n}=this.options;if(e||(t=t.toLowerCase()),this.pattern===t){let e={isMatch:!0,score:0};return n&&(e.indices=[[0,t.length-1]]),e}const{location:s,distance:i,threshold:r,findAllMatches:c,minMatchCharLength:o,ignoreLocation:h}=this.options;let a=[],l=0,u=!1;this.chunks.forEach((({pattern:e,alphabet:d,startIndex:g})=>{const{isMatch:f,score:p,indices:m}=x(t,e,d,{location:s+g,distance:i,threshold:r,findAllMatches:c,minMatchCharLength:o,includeMatches:n,ignoreLocation:h});f&&(u=!0),l+=p,f&&m&&(a=[...a,...m])}));let d={isMatch:u,score:u?l/this.chunks.length:1};return u&&n&&(d.indices=a),d}}class k{constructor(t){this.pattern=t}static isMultiMatch(t){return _(t,this.multiRegex)}static isSingleMatch(t){return _(t,this.singleRegex)}search(){}}function _(t,e){const n=t.match(e);return n?n[1]:null}class v extends k{constructor(t,{location:e=g.location,threshold:n=g.threshold,distance:s=g.distance,includeMatches:i=g.includeMatches,findAllMatches:r=g.findAllMatches,minMatchCharLength:c=g.minMatchCharLength,isCaseSensitive:o=g.isCaseSensitive,ignoreLocation:h=g.ignoreLocation}={}){super(t),this._bitapSearch=new L(t,{location:e,threshold:n,distance:s,includeMatches:i,findAllMatches:r,minMatchCharLength:c,isCaseSensitive:o,ignoreLocation:h})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(t){return this._bitapSearch.searchIn(t)}}class S extends k{constructor(t){super(t)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(t){let e,n=0;const s=[],i=this.pattern.length;for(;(e=t.indexOf(this.pattern,n))>-1;)n=e+i,s.push([e,n-1]);const r=!!s.length;return{isMatch:r,score:r?0:1,indices:s}}}const w=[class extends k{constructor(t){super(t)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(t){const e=t===this.pattern;return{isMatch:e,score:e?0:1,indices:[0,this.pattern.length-1]}}},S,class extends k{constructor(t){super(t)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(t){const e=t.startsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[0,this.pattern.length-1]}}},class extends k{constructor(t){super(t)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(t){const e=!t.startsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[0,t.length-1]}}},class extends k{constructor(t){super(t)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(t){const e=!t.endsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[0,t.length-1]}}},class extends k{constructor(t){super(t)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(t){const e=t.endsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[t.length-this.pattern.length,t.length-1]}}},class extends k{constructor(t){super(t)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(t){const e=-1===t.indexOf(this.pattern);return{isMatch:e,score:e?0:1,indices:[0,t.length-1]}}},v],C=w.length,I=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/;const $=new Set([v.type,S.type]);class A{constructor(t,{isCaseSensitive:e=g.isCaseSensitive,includeMatches:n=g.includeMatches,minMatchCharLength:s=g.minMatchCharLength,ignoreLocation:i=g.ignoreLocation,findAllMatches:r=g.findAllMatches,location:c=g.location,threshold:o=g.threshold,distance:h=g.distance}={}){this.query=null,this.options={isCaseSensitive:e,includeMatches:n,minMatchCharLength:s,findAllMatches:r,ignoreLocation:i,location:c,threshold:o,distance:h},this.pattern=e?t:t.toLowerCase(),this.query=function(t,e={}){return t.split("|").map((t=>{let n=t.trim().split(I).filter((t=>t&&!!t.trim())),s=[];for(let t=0,i=n.length;t<i;t+=1){const i=n[t];let r=!1,c=-1;for(;!r&&++c<C;){const t=w[c];let n=t.isMultiMatch(i);n&&(s.push(new t(n,e)),r=!0)}if(!r)for(c=-1;++c<C;){const t=w[c];let n=t.isSingleMatch(i);if(n){s.push(new t(n,e));break}}}return s}))}(this.pattern,this.options)}static condition(t,e){return e.useExtendedSearch}searchIn(t){const e=this.query;if(!e)return{isMatch:!1,score:1};const{includeMatches:n,isCaseSensitive:s}=this.options;t=s?t:t.toLowerCase();let i=0,r=[],c=0;for(let s=0,o=e.length;s<o;s+=1){const o=e[s];r.length=0,i=0;for(let e=0,s=o.length;e<s;e+=1){const s=o[e],{isMatch:h,indices:a,score:l}=s.search(t);if(!h){c=0,i=0,r.length=0;break}if(i+=1,c+=l,n){const t=s.constructor.type;$.has(t)?r=[...r,...a]:r.push(a)}}if(i){let t={isMatch:!0,score:c/i};return n&&(t.indices=r),t}}return{isMatch:!1,score:1}}}const E=[];function b(t,e){for(let n=0,s=E.length;n<s;n+=1){let s=E[n];if(s.condition(t,e))return new s(t,e)}return new L(t,e)}const F="$and",N="$or",R="$path",O="$val",j=t=>!(!t[F]&&!t[N]),W=t=>({[F]:Object.keys(t).map((e=>({[e]:t[e]})))});function z(n,s,{auto:r=!0}={}){const c=n=>{let o=Object.keys(n);const h=(t=>!!t[R])(n);if(!h&&o.length>1&&!j(n))return c(W(n));if((e=>!t(e)&&i(e)&&!j(e))(n)){const t=h?n[R]:o[0],i=h?n[O]:n[t];if(!e(i))throw new Error((t=>`Invalid value for key ${t}`)(t));const c={keyId:d(t),pattern:i};return r&&(c.searcher=b(i,s)),c}let a={children:[],operator:o[0]};return o.forEach((e=>{const s=n[e];t(s)&&s.forEach((t=>{a.children.push(c(t))}))})),a};return j(n)||(n=W(n)),c(n)}function K(t,e){const n=t.matches;e.matches=[],r(n)&&n.forEach((t=>{if(!r(t.indices)||!t.indices.length)return;const{indices:n,value:s}=t;let i={indices:n,value:s};t.key&&(i.key=t.key.src),t.idx>-1&&(i.refIndex=t.idx),e.matches.push(i)}))}function P(t,e){e.score=t.score}class q{constructor(t,e={},n){this.options={...g,...e},this.options.useExtendedSearch,this._keyStore=new a(this.options.keys),this.setCollection(t,n)}setCollection(t,e){if(this._docs=t,e&&!(e instanceof p))throw new Error("Incorrect 'index' type");this._myIndex=e||m(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(t){r(t)&&(this._docs.push(t),this._myIndex.add(t))}remove(t=(()=>!1)){const e=[];for(let n=0,s=this._docs.length;n<s;n+=1){const i=this._docs[n];t(i,n)&&(this.removeAt(n),n-=1,s-=1,e.push(i))}return e}removeAt(t){this._docs.splice(t,1),this._myIndex.removeAt(t)}getIndex(){return this._myIndex}search(t,{limit:s=-1}={}){const{includeMatches:i,includeScore:r,shouldSort:c,sortFn:o,ignoreFieldNorm:h}=this.options;let a=e(t)?e(this._docs[0])?this._searchStringList(t):this._searchObjectList(t):this._searchLogical(t);return function(t,{ignoreFieldNorm:e=g.ignoreFieldNorm}){t.forEach((t=>{let n=1;t.matches.forEach((({key:t,norm:s,score:i})=>{const r=t?t.weight:null;n*=Math.pow(0===i&&r?Number.EPSILON:i,(r||1)*(e?1:s))})),t.score=n}))}(a,{ignoreFieldNorm:h}),c&&a.sort(o),n(s)&&s>-1&&(a=a.slice(0,s)),function(t,e,{includeMatches:n=g.includeMatches,includeScore:s=g.includeScore}={}){const i=[];return n&&i.push(K),s&&i.push(P),t.map((t=>{const{idx:n}=t,s={item:e[n],refIndex:n};return i.length&&i.forEach((e=>{e(t,s)})),s}))}(a,this._docs,{includeMatches:i,includeScore:r})}_searchStringList(t){const e=b(t,this.options),{records:n}=this._myIndex,s=[];return n.forEach((({v:t,i:n,n:i})=>{if(!r(t))return;const{isMatch:c,score:o,indices:h}=e.searchIn(t);c&&s.push({item:t,idx:n,matches:[{score:o,value:t,norm:i,indices:h}]})})),s}_searchLogical(t){const e=z(t,this.options),n=(t,e,s)=>{if(!t.children){const{keyId:n,searcher:i}=t,r=this._findMatches({key:this._keyStore.get(n),value:this._myIndex.getValueForItemAtKeyId(e,n),searcher:i});return r&&r.length?[{idx:s,item:e,matches:r}]:[]}const i=[];for(let r=0,c=t.children.length;r<c;r+=1){const c=t.children[r],o=n(c,e,s);if(o.length)i.push(...o);else if(t.operator===F)return[]}return i},s=this._myIndex.records,i={},c=[];return s.forEach((({$:t,i:s})=>{if(r(t)){let r=n(e,t,s);r.length&&(i[s]||(i[s]={idx:s,item:t,matches:[]},c.push(i[s])),r.forEach((({matches:t})=>{i[s].matches.push(...t)})))}})),c}_searchObjectList(t){const e=b(t,this.options),{keys:n,records:s}=this._myIndex,i=[];return s.forEach((({$:t,i:s})=>{if(!r(t))return;let c=[];n.forEach(((n,s)=>{c.push(...this._findMatches({key:n,value:t[s],searcher:e}))})),c.length&&i.push({idx:s,item:t,matches:c})})),i}_findMatches({key:e,value:n,searcher:s}){if(!r(n))return[];let i=[];if(t(n))n.forEach((({v:t,i:n,n:c})=>{if(!r(t))return;const{isMatch:o,score:h,indices:a}=s.searchIn(t);o&&i.push({score:h,key:e,value:t,idx:n,norm:c,indices:a})}));else{const{v:t,n:r}=n,{isMatch:c,score:o,indices:h}=s.searchIn(t);c&&i.push({score:o,key:e,value:t,norm:r,indices:h})}return i}}q.version="6.6.2",q.createIndex=m,q.parseIndex=function(t,{getFn:e=g.getFn,fieldNormWeight:n=g.fieldNormWeight}={}){const{keys:s,records:i}=t,r=new p({getFn:e,fieldNormWeight:n});return r.setKeys(s),r.setIndexRecords(i),r},q.config=g,function(...t){E.push(...t)}(A);export{q as default};