<div class="container-xl">
  <!-- Page title -->
  <div class="page-header d-print-none">
    <div class="row align-items-center">
      <div class="col">
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="javascript:show_add_torrent_remove_task_modal()" class="btn btn-primary d-none d-sm-inline-flex">
            <i class="ti ti-plus fs-2"></i>
            新增删种任务
          </a>
          <a href="javascript:show_add_torrent_remove_task_modal()" class="btn btn-primary d-sm-none btn-icon">
            <i class="ti ti-plus"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- 业务页面代码 -->
{% if Count > 0 %}
<div class="page-body">
  <div class="container-xl">
    <div class="row row-cards">
      {% for Id, Attr in TorrentRemoveTasks.items() %}
      <div class="card">
        <div class="card-header">
          <div>
            {% if Attr.enabled %}
            <span class="badge bg-green"></span>
            {% else %}
            <span class="badge bg-red"></span>
            {% endif %}
          </div>
          <a href="javascript:void(0)" onclick="show_torrentremove_detail({{ Attr.id }})" style="text-decoration-line: none; color: unset"
             title="展开/折叠" data-bs-toggle="tooltip">
            <div class="ms-3">
                <h3 class="card-title">{{ Attr.name }}</h3>
            </div>
          </a>
          <div class="ms-2 d-none d-sm-block">
            <a href="javascript:void(0)" onclick="run_torrent_remove_now('{{ Attr.id }}')" class="btn-icon" title="立即运行任务"
              data-bs-toggle="tooltip">
              <i class="ti ti-bolt fs-2"></i>
            </a>
          </div>
          <a href="#" class="link-secondary ms-auto d-sm-none" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="ti ti-dots fs-2"></i>
          </a>
          <div class="dropdown-menu dropdown-menu-end">
            <button class="dropdown-item text-info" onclick="run_torrent_remove_now('{{ Attr.id }}')">
              立即运行
            </button>
            <button class="dropdown-item" onclick="show_remove_torrents_modal('{{ Attr.id }}')">
              预览
            </button>
            <button class="dropdown-item" onclick="show_edit_torrent_remove_task_modal('{{ Attr.id }}')">
              编辑
            </button>
            <button class="dropdown-item text-danger" onclick="delete_torrent_remove_task('{{ Attr.id }}', '{{ Attr.name }}')">
              删除
            </button>
          </div>
          <div class="card-actions btn-actions d-none d-sm-block">
            <a href="javascript:void(0)" onclick="show_torrentremove_detail({{ Attr.id }})" class="btn-action"
               title="展开/折叠" data-bs-toggle="tooltip">
              <i class="ti ti-menu-2 fs-2"></i>
            </a>
            <a href="javascript:show_remove_torrents_modal('{{ Attr.id }}')" class="btn-action"
               title="预览删种" data-bs-toggle="tooltip">
              <i class="ti ti-eye fs-2"></i>
            </a>
            <a href="javascript:show_edit_torrent_remove_task_modal('{{ Attr.id }}')" class="btn-action"
               title="编辑设置" data-bs-toggle="tooltip">
              <i class="ti ti-edit fs-2"></i>
            </a>
            <a href="javascript:delete_torrent_remove_task('{{ Attr.id }}', '{{ Attr.name }}')" class="btn-action"
               title="删除设置" data-bs-toggle="tooltip">
              <i class="ti ti-x fs-2"></i>
            </a>
          </div>
        </div>
        <div class="card-body" id="detail_{{ Attr.id }}"
            style="display: {% if Count > 3 %}none{% else %}block{% endif %};">
          <div class="datagrid">
            <div class="datagrid-item">
              <div class="datagrid-title">下载器</div>
              <div class="datagrid-content">
                <span class="badge mb-1 bg-blue">{{ Attr.downloader_name }}</span>
              </div>
            </div>
            <div class="datagrid-item">
              <div class="datagrid-title">管理限制</div>
              <div class="datagrid-content">
              {% if Attr.onlynastool %}
                <span class="badge mb-1">隔离</span>
              {% endif %}
              {% if Attr.samedata %}
                <span class="badge mb-1">处理辅种</span>
              {% endif %}
              </div>
            </div>
            <div class="datagrid-item">
              <div class="datagrid-title">刷新间隔</div>
              <div class="datagrid-content">
                {{ Attr.interval }} 分钟
              </div>
            </div>
            <div class="datagrid-item">
              <div class="datagrid-title">动作</div>
              <div class="datagrid-content">
                {% if Attr.action == 1 %}
                <span class="badge bg-blue">暂停任务</span>
                {% elif Attr.action == 2 %}
                <span class="badge bg-orange">删除任务</span>
                {% elif Attr.action == 3 %}
                <span class="badge bg-red">删除任务及文件</span>
                {% endif %}
              </div>
            </div>
            <div class="datagrid-item">
              <div class="datagrid-title">分享率</div>
              <div class="datagrid-content">
              {% if Attr.config.ratio %}
                {{ Attr.config.ratio }} +
              {% endif %}
              </div>
            </div>
            <div class="datagrid-item">
              <div class="datagrid-title">做种时间</div>
              <div class="datagrid-content">
              {% if Attr.config.seeding_time %}
                {{ Attr.config.seeding_time }} 小时 +
              {% endif %}
              </div>
            </div>
            <div class="datagrid-item">
              <div class="datagrid-title">平均做种速度</div>
              <div class="datagrid-content">
              {% if Attr.config.upload_avs %}
                {{ Attr.config.upload_avs }} KB/s -
              {% endif %}
              </div>
            </div>
            <div class="datagrid-item">
              <div class="datagrid-title">大小</div>
              <div class="datagrid-content">
              {% if Attr.config.size %}
                {{ Attr.config.size[0] }}-{{ Attr.config.size[-1] }} GB
              {% endif %}
              </div>
            </div>
            <div class="datagrid-item">
              <div class="datagrid-title">保存路径关键词</div>
              <div class="datagrid-content">
              {% if Attr.config.savepath_key %}
                <span class="badge bg-orange text-wrap text-break text-start">{{ Attr.config.savepath_key }}</span>
              {% endif %}
              </div>
            </div>
            <div class="datagrid-item">
              <div class="datagrid-title">tracker关键词</div>
              <div class="datagrid-content">
              {% if Attr.config.tracker_key %}
                <span class="badge bg-orange text-wrap text-break text-start">{{ Attr.config.tracker_key }}</span>
              {% endif %}
              </div>
            </div>
            {% if Attr.downloader_type == "qbittorrent" %}
              <div class="datagrid-item">
                <div class="datagrid-title">分类</div>
                <div class="datagrid-content">
                  {% if Attr.config.qb_category %}
                  {% for Category in Attr.config.qb_category %}
                  <span class="badge mb-1">{{ Category }}</span>
                  {% endfor %}
                  {% endif %}
                </div>
              </div>
              <div class="datagrid-item">
                <div class="datagrid-title">种子状态</div>
                <div class="datagrid-content">
                  {% if Attr.config.qb_state %}
                  {% for State in Attr.config.qb_state %}
                  <span class="badge bg-blue mb-1">{{ State }}</span>
                  {% endfor %}
                  {% endif %}
                </div>
              </div>
            {% elif Attr.downloader_type == "transmission" %}
              <div class="datagrid-item">
                <div class="datagrid-title">错误信息关键词</div>
                <div class="datagrid-content">
                {% if Attr.config.tr_error_key %}
                  <span class="badge bg-orange text-wrap text-break text-start">{{ Attr.config.tr_error_key }}</span>
                {% endif %}
                </div>
              </div>
              <div class="datagrid-item">
                <div class="datagrid-title">种子状态</div>
                <div class="datagrid-content">
                  {% if Attr.config.tr_state %}
                  {% for State in Attr.config.tr_state %}
                  <span class="badge bg-blue mb-1">{{ State }}</span>
                  {% endfor %}
                  {% endif %}
                </div>
              </div>
            {% endif %}
            <div class="datagrid-item">
              <div class="datagrid-title">状态</div>
              <div class="datagrid-content">
                {% if Attr.enabled %}
                <span class="badge bg-green">正在运行</span>
                {% else %}
                <span class="badge bg-red">已停用</span>
                {% endif %}
              </div>
            </div>
            <div class="datagrid-item">
              <div class="datagrid-title">标签</div>
              <div class="datagrid-content">
                {% if Attr.config.tags %}
                {% for Tag in Attr.config.tags %}
                <span class="badge mb-1">{{ Tag }}</span>
                {% endfor %}
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
</div>
{% else %}
<nodata-found title="没有任务" text="当前没有正在运行的自动删种任务。"></nodata-found>
{% endif %}
<div class="modal modal-blur fade" id="modal-torrent-remove-task" tabindex="-1" role="dialog" aria-hidden="true"
  data-bs-backdrop="static" data-bs-keyboard="false">
  <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="modal-torrent-remove-task-title">新增删种任务</h5>
        <input type="hidden" id="torrent_remove_task_id" />
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-lg-6">
            <div class="mb-3">
              <label class="form-label required">名称</label>
              <input type="text" id="torrent_remove_name" class="form-control" placeholder="别名">
            </div>
          </div>
          <div class="col-lg-6">
            <div class="mb-3">
              <label class="form-label required">下载器</label>
              <select class="form-select" id="torrent_remove_downloader" onchange="show_torrent_remove_downloader_type($(this).val())">
                <option value="">请选择</option>
                {% for Id, Attr in Downloaders.items() %}
                <option value="{{ Id }}">{{ Attr.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-label">动作</label>
              <select class="form-select" id="torrent_remove_action">
                <option value="1" selected>暂停种子</option>
                <option value="2">删除种子</option>
                <option value="3">删除种子及文件</option>
              </select>
            </div>
          </div>
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-label required">运行间隔（分钟） <span class="form-help" title="执行自动删种任务的间隔时间"
                  data-bs-toggle="tooltip">?</span></label>
              <input type="text" id="torrent_remove_interval" class="form-control" placeholder="分钟">
            </div>
          </div>
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-label">状态</label>
              <select class="form-select" id="torrent_remove_enabled">
                <option value="1">启用</option>
                <option value="0" selected>停用</option>
              </select>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-label">处理辅种 <span class="form-help" title="若种子名称及大小与满足其他删种条件的种子相同，则一并处理"
                  data-bs-toggle="tooltip">?</span></label>
              <select class="form-select" id="torrent_remove_samedata">
                <option value="0" selected>否</option>
                <option value="1" >是</option>
              </select>
            </div>
          </div>
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-label">隔离 <span class="form-help" title="只管理含有NASTOOL标签的种子"
                  data-bs-toggle="tooltip">?</span></label>
              <select class="form-select" id="torrent_remove_onlynastool">
                <option value="0">否</option>
                <option value="1" selected>是</option>
              </select>
            </div>
          </div>
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-label">种子大小 <span class="form-help" title="种子大小在设置范围内视为满足该条件，留空为不启用"
                data-bs-toggle="tooltip">?</span></label>
              <input type="text" id="torrent_remove_size" class="form-control" placeholder="GB，例如如1-10">
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-label">分享率 <span class="form-help" title="种子分享率大于等于设置值视为满足该条件，留空为不启用"
                data-bs-toggle="tooltip">?</span></label>
              <input type="text" id="torrent_remove_ratio" class="form-control" placeholder="保留一位小数">
            </div>
          </div>
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-label">做种时间 <span class="form-help" title="种子做种时间（未完成种子从添加时间开始，完成种子从完成时间开始）大于等于设置值视为满足该条件，留空为不启用"
                data-bs-toggle="tooltip">?</span></label>
              <input type="text" id="torrent_remove_seeding_time" class="form-control" placeholder="小时">
            </div>
          </div>
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-label">平均上传速度 <span class="form-help" title="种子平均上传速度小于等于设置值视为满足该条件，留空为不启用"
                data-bs-toggle="tooltip">?</span></label>
              <input type="text" id="torrent_remove_upload_avs" class="form-control" placeholder="KB/s">
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-label">标签 <span class="form-help" title="设置多个标签时，种子包含所有标签视为满足该条件，留空为不启用"
                data-bs-toggle="tooltip">?</span></label>
              <input type="text" id="torrent_remove_tags" class="form-control" placeholder="多个标签用;分隔">
            </div>
          </div>
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-label">保存路径关键词 <span class="form-help" title="种子保存路径含有关键词视为满足该条件，留空为不启用"
                data-bs-toggle="tooltip">?</span></label>
              <input type="text" id="torrent_remove_savepath_key" class="form-control" placeholder="支持正则表达式">
            </div>
          </div>
          <div class="col-lg-4">
            <div class="mb-3">
              <label class="form-label">tracker关键词 <span class="form-help" title="种子第一个活动tracker含有关键词视为满足该条件，留空为不启用"
                data-bs-toggle="tooltip">?</span></label>
              <input type="text" id="torrent_remove_tracker_key" class="form-control" placeholder="支持正则表达式">
            </div>
          </div>
        </div>
        <div id="div_qb_config" class="row">
          <div class="col-lg-7">
            <div class="mb-3">
              <label class="form-label">种子状态 <span class="form-help"
                title=
                "{% for Id, Attr in DownloaderConfig.qbittorrent.torrent_state.items() %}
                {{ Id }}：{{ Attr }}<br>
                {% endfor %}
                <br>设置多个状态时，种子属于其中任一状态视为满足该条件，留空为不启用"
                data-bs-toggle="tooltip"
                data-bs-html="true">?</span></label>
              <input type="text" id="torrent_remove_qb_state" class="form-control" placeholder="多个状态用;分隔">
            </div>
          </div>
          <div class="col-lg-5">
            <div class="mb-3">
              <label class="form-label">分类 <span class="form-help" title="设置多个分类时，种子属于其中任一分类视为满足该条件，留空为不启用"
                data-bs-toggle="tooltip">?</span></label>
              <input type="text" id="torrent_remove_qb_category" class="form-control" placeholder="多个分类用;分隔">
            </div>
          </div>
        </div>
        <div id="div_tr_config" class="row">
          <div class="col-lg-7">
            <div class="mb-3">
              <label class="form-label">种子状态 <span class="form-help"
                title="{% for Id, Attr in DownloaderConfig.transmission.torrent_state.items() %}
                {{ Id }}：{{ Attr }}<br>
                {% endfor %}
                <br>设置多个状态时，种子属于其中任一状态视为满足该条件，留空为不启用"
                data-bs-toggle="tooltip"
                data-bs-html="true">?</span></label>
              <input type="text" id="torrent_remove_tr_state" class="form-control" placeholder="多个状态用;分隔">
            </div>
          </div>
          <div class="col-lg-5">
            <div class="mb-3">
              <label class="form-label">错误信息关键词 <span class="form-help" title="种子错误信息含有关键词视为满足该条件，留空为不启用"
                data-bs-toggle="tooltip">?</span></label>
              <input type="text" id="torrent_remove_tr_error_key" class="form-control" placeholder="支持正则表达式">
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button id="download_setting_close_btn" class="btn btn-link" data-bs-dismiss="modal">
          取消
        </button>
        <button id="add_or_edit_torrent_remove_task_btn" class="btn btn-primary ms-auto"
          onclick="add_or_edit_torrent_remove_task()">
          保存
        </button>
      </div>
    </div>
  </div>
</div>
<div class="modal modal-blur fade" id="modal-remove-torrents" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">预处理种子列表</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="table-responsive table-modal-body">
        <table id="table-remove-torrents" class="table table-vcenter card-table">
        </table>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript">

  var DownloaderTypes = {{ Downloaders|safe }};

  // 折叠任务详情
  function show_torrentremove_detail(taskid) {
    $(`#detail_${taskid}`).slideToggle();
  }

  // 重置删种任务表单
  function reset_torrent_remove_config(){
    $("#torrent_remove_name").val('');
    $("#torrent_remove_downloader").val('');
    $("#torrent_remove_action").val('1');
    $("#torrent_remove_interval").val('');
    $("#torrent_remove_enabled").val('0');
    $("#torrent_remove_samedata").val('0');
    $("#torrent_remove_onlynastool").val('1');
    $("#torrent_remove_size").val('');
    $("#torrent_remove_ratio").val('');
    $("#torrent_remove_seeding_time").val('');
    $("#torrent_remove_upload_avs").val('');
    $("#torrent_remove_tags").val('');
    $("#torrent_remove_savepath_key").val('');
    $("#torrent_remove_tracker_key").val('');
    $("#torrent_remove_qb_category").val('');
    $("#torrent_remove_qb_state").val('');
    $("#torrent_remove_tr_error_key").val('');
    $("#torrent_remove_tr_state").val('');
  }

  // 显示新增任务
  function show_add_torrent_remove_task_modal(){
    let downloaders = {{ Downloaders|safe }};
    if (!Object.keys(downloaders).length) {
      show_fail_modal("请先添加下载器！");
      return;
    }
    $("#torrent_remove_task_id").val('');
    $("#modal-torrent-remove-task-title").val('新增删种任务');
    reset_torrent_remove_config();
    show_torrent_remove_downloader_type("");
    $("#modal-torrent-remove-task").modal('show');
  }

  // 显示编辑任务
  function show_edit_torrent_remove_task_modal(tid){
    $("#torrent_remove_task_id").val(tid);
    axios_post("get_torrent_remove_task", { tid: tid }, function (ret) {
      if (ret.code == 0) {
        $("#modal-download-setting-title").val('编辑下载设置');
        reset_torrent_remove_config();
        $("#torrent_remove_name").val(ret.detail.name);
        $("#torrent_remove_downloader").val(ret.detail.downloader);
        $("#torrent_remove_action").val(ret.detail.action);
        $("#torrent_remove_interval").val(ret.detail.interval);
        $("#torrent_remove_enabled").val(ret.detail.enabled);
        $("#torrent_remove_samedata").val(ret.detail.samedata);
        $("#torrent_remove_onlynastool").val(ret.detail.onlynastool);
        if (ret.detail.config.size.length) {
          let minsize = ret.detail.config.size[0];
          let maxsize = ret.detail.config.size[1];
          $("#torrent_remove_size").val(`${minsize}-${maxsize}`);
        } else {
          $("#torrent_remove_size").val('');
        }
        if (ret.detail.config.ratio) {
          $("#torrent_remove_ratio").val(ret.detail.config.ratio);
        } else {
          $("#torrent_remove_ratio").val('');
        }
        if (ret.detail.config.seeding_time) {
          $("#torrent_remove_seeding_time").val(ret.detail.config.seeding_time);
        } else {
          $("#torrent_remove_seeding_time").val('');
        }
        if (ret.detail.config.upload_avs) {
          $("#torrent_remove_upload_avs").val(ret.detail.config.upload_avs);
        } else {
          $("#torrent_remove_upload_avs").val('');
        }
        $("#torrent_remove_tags").val(ret.detail.config.tags.join(';'));
        $("#torrent_remove_savepath_key").val(ret.detail.config.savepath_key);
        $("#torrent_remove_tracker_key").val(ret.detail.config.tracker_key);
        show_torrent_remove_downloader_type(ret.detail.downloader, ret.detail.config);
        $("#modal-torrent-remove-task").modal('show');
      }
    });
  }

  // 保存新建/编辑任务
  function add_or_edit_torrent_remove_task(){
    let config = input_select_GetVal("modal-torrent-remove-task", "torrent_remove_");
    if (!config.name) {
      $("#torrent_remove_name").addClass("is-invalid");
      return;
    } else {
      $("#torrent_remove_name").removeClass("is-invalid");
    }
    if (!config.interval || isNaN(config.interval)) {
      $("#torrent_remove_interval").addClass("is-invalid");
      return;
    } else {
      $("#torrent_remove_interval").removeClass("is-invalid");
    }
    if (config.ratio && isNaN(config.ratio)) {
      $("#torrent_remove_ratio").addClass("is-invalid");
      return;
    } else {
      $("#torrent_remove_ratio").removeClass("is-invalid");
    }
    if (config.seeding_time && isNaN(config.seeding_time)) {
      $("#torrent_remove_seeding_time").addClass("is-invalid");
      return;
    } else {
      $("#torrent_remove_seeding_time").removeClass("is-invalid");
    }
    if (config.upload_avs && isNaN(config.upload_avs)) {
      $("#torrent_remove_upload_avs").addClass("is-invalid");
      return;
    } else {
      $("#torrent_remove_upload_avs").removeClass("is-invalid");
    }
    if (!config.downloader) {
      $("#download_setting_downloader").addClass("is-invalid");
      return;
    } else {
      $("#download_setting_downloader").removeClass("is-invalid");
    }
    let params = {...{tid: $("#torrent_remove_task_id").val()}, ...config};
    $("#add_or_edit_torrent_remove_task_btn").text("保存中").attr("disabled", true);
    axios_post("update_torrent_remove_task", params, function (ret) {
      $("#modal-torrent-remove-task").modal('hide');
      if (ret.code == 0) {
        $("#add_or_edit_torrent_remove_task_btn").text("保存").attr("disabled", false);
        window_history_refresh();
      } else {
        show_fail_modal(ret.msg, function () {
          $("#modal-torrent-remove-task").modal('show');
          $("#add_or_edit_torrent_remove_task_btn").text("保存").attr("disabled", false);
        });
      }
    });
  }

  // 删除任务
  function delete_torrent_remove_task(tid, name){
    show_confirm_modal(`删除下载设置 ${name} ？`, function () {
      hide_confirm_modal();
      axios_post("delete_torrent_remove_task", { "tid": tid }, function (ret) {
        window_history_refresh();
      });
    });
  }

  // 显示满足条件种子
  function show_remove_torrents_modal(tid){
    axios_post("get_remove_torrents", { "tid": tid }, function (ret) {
      if (ret.code == 0) {
        let torrents = ret.data;
        let thead = `<thead><tr><th>共${torrents.length}个种子</th></tr></thead>`;
        let tbody = '';
        for (let torrent of torrents) {
          let tb = `<tr><td><span>${torrent.name}</span>
                    <br><span class="badge badge-outline text-blue mb-1">${torrent.site}</span>
                    <span class="badge badge-outline text-orange mb-1">${(torrent.size/1024/1024/1024).toFixed(2)} GB</span></td></tr>`;
          tbody = `${tbody}${tb}`;
        }
        tbody = `<tbody>${tbody}</tbody>`;
        $("#table-remove-torrents").empty().append(`${thead}${tbody}`);
        $("#modal-remove-torrents").modal('show');
      } else {
        let content = `<div class="empty"><p class="empty-title">${ret.msg}</p></div>`;
        $("#table-remove-torrents").empty().append(content);
        $("#modal-remove-torrents").modal('show');
      }
    });
  }

  // 单选框事件
  $('input[type=radio][name=downloader]').change(function () {
    let use = this.value;
    show_torrent_remove_downloader_type(use);
  });

  // 立即运行任务
  function run_torrent_remove_now(tid) {
    axios_post("auto_remove_torrents", { "tid": tid }, function (ret) {
      show_success_modal("任务运行完成！", function () {
        window_history_refresh();
      });
    });
  }

  // 联动事件
  function show_torrent_remove_downloader_type(downloader_id, config) {
    let downloader_type = "";
    if (downloader_id) {
      downloader_type = DownloaderTypes[downloader_id].type;
    }
    if (config) {
      $("#torrent_remove_qb_category").val(config.qb_category.join(';'));
      $("#torrent_remove_qb_state").val(config.qb_state.join(';'));
      $("#torrent_remove_tr_error_key").val(config.tr_error_key);
      $("#torrent_remove_tr_state").val(config.tr_state.join(';'));
    }
    let qb_config_obj = $("#div_qb_config");
    let tr_config_obj = $("#div_tr_config");
    switch (downloader_type) {
      case 'qbittorrent':
        qb_config_obj.show();
        tr_config_obj.hide();
        break;
      case 'transmission':
        qb_config_obj.hide();
        tr_config_obj.show();
        break;
      default:
        qb_config_obj.hide();
        tr_config_obj.hide();
    }
  }
</script>