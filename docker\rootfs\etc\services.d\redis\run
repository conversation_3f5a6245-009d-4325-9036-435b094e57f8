#!/usr/bin/with-contenv bash
# shellcheck shell=bash

function __debug {

exec \
    s6-notifyoncheck -d -n 300 -w 1000 -c "nc -z localhost 6379" \
    s6-setuidgid root $(which redis-server)

}

function __false {

exec \
    s6-notifyoncheck -d -n 300 -w 1000 -c "nc -z localhost 6379" \
    s6-setuidgid root $(which redis-server) > /dev/null 2>&1

}

if [ -f ${NASTOOL_CONFIG} ]; then
    NT_LOG=$(awk -F"[' ]+" '/loglevel/{print $3}' /config/config.yaml)
    if [[ "${NT_LOG}" == "debug" ]]; then
        __debug
    else
        __false
    fi
else
    __false
fi
