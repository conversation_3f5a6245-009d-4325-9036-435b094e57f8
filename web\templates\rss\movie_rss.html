<style>
  .card-progress {
    position: absolute;
    width: 100%;
    bottom: 0;
  }
  .right-top-del-btn {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff5252, #d32f2f);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    border: none;
    z-index: 10;
    transition: all 0.3s ease;
  }

  .right-top-del-btn:hover {
    transform: scale(1.15) rotate(90deg);
    background: linear-gradient(135deg, #ff6b6b, #f44336);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.5);
  }

</style>
<div class="container-xl">
  <div class="page-header d-print-none">
    <div class="row align-items-center">
      <div class="col">
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="javascript:show_add_rss_media_modal('{{Type}}')" class="btn btn-primary d-none d-sm-inline-block">
            <i class="ti ti-plus"></i>
            新增订阅
          </a>
          <a href="javascript:show_add_rss_media_modal('{{Type}}')" class="btn btn-primary d-sm-none btn-icon">
            <i class="ti ti-plus"></i>
          </a>
          <a href="javascript:show_default_rss_setting_modal('{{Type}}')"
            class="btn btn-twitter d-none d-sm-inline-block">
            <i class="ti ti-server-2"></i>
            默认设置
          </a>
          <a href="javascript:show_default_rss_setting_modal('{{Type}}')" class="btn btn-twitter d-sm-none btn-icon">
            <i class="ti ti-server-2"></i>
          </a>
          <a href="javascript:navmenu('rss_history?t={{Type}}')" class="btn d-none d-sm-inline-block">
            <i class="ti ti-history"></i>
            订阅历史
          </a>
          <a href="javascript:navmenu('rss_history?t={{Type}}')" class="btn d-sm-none btn-icon" title="RSS解析器">
            <i class="ti ti-history"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
{% if Count > 0 %}
<div class="page-body">
  <div class="container-xl">
    <div class="d-grid gap-3 grid-info-card">
      {% for Id, Attr in Items.items() %}
      <div class="card p-0">
        <div class="card-body media-box" style="background-image: url({{ Attr.image }});">
          <div class="ribbon ribbon-top ribbon-bookmark bg-purple">{{Attr.vote}}</div>
          <button class="right-top-del-btn" title="删除任务" onclick="remove_rss_click('{{Attr.name}}','{{Attr.year}}', '{{Type}}','{{ Id }}', 'movie_rss')">
            <i class="ti ti-x"></i>
          </button>
          <div class="row align-items-stretch media-content card-text">
            <div class="col-auto p-0 card">
              <img src="{{ Attr.poster }}" class="rounded" alt="" style="max-width: 80px;">
            </div>
            <div class="col" style="display: flex;flex-direction: column;overflow: hidden;">
              <h3 class="card-title mb-1 border-bottom">
                <a href='javascript:navmenu("media_detail?type={{Type}}&id={{ Attr.tmdbid }}")'
                  class="text-reset card-text">
                  {% if Type == 'MOV' %}
                  {{ Attr.name }}({{ Attr.year }})
                  {% else %}
                  {{ Attr.name }}{% if Attr.season != "S00" %} - {{ Attr.season }}{% endif %}
                  {% endif %}
                </a>
              </h3>
              <div class="card-body text-center border-bottom" style="padding: 0;flex-grow: 1;">
                <div{% if Attr.over_edition or Attr.filter_restype or Attr.filter_pix or Attr.filter_team or
                  (Attr.download_setting and Attr.download_setting !=-1) or Attr.rss_sites or Attr.search_sites %}
                  class="text-muted pt-1 mt-1" {% else %} class=“text-muted" {% endif %}>
                  <small>
                    {% if Attr.over_edition %}
                    <span class="badge me-1 mb-1" title="已开启洗版">洗版</span>
                    {% endif %}
                    {% if Attr.download_setting|string in DownloadSettings %}
                    <span class="badge me-1 mb-1" title="下载设置">
                      {{ DownloadSettings[Attr.download_setting|string].name }}
                    </span>
                    {% endif %}
                    {% if Attr.filter_restype %}
                    <span class="badge bg-yellow me-1 mb-1" title="质量">
                      {{ Attr.filter_restype }}
                    </span>
                    {% endif %}
                    {% if Attr.filter_pix %}
                    <span class="badge bg-yellow me-1 mb-1" title="分辨率">
                      {{ Attr.filter_pix }}
                    </span>
                    {% endif %}
                    {% if Attr.filter_team %}
                    <span class="badge bg-cyan me-1 mb-1 text-wrap text-start" title="制作组/字幕组">
                      {{ Attr.filter_team }}
                    </span>
                    {% endif %}
                    {% if Attr.filter_rule|string in RuleGroups %}
                    <span class="badge bg-orange me-1 mb-1" title="过滤规则">
                      {{ RuleGroups[Attr.filter_rule|string] }}
                    </span>
                    {% endif %}
                    {% if Attr.filter_include %}
                    <span class="badge bg-green me-1 mb-1 text-wrap text-start" title="包含">
                      {{ Attr.filter_include }}
                    </span>
                    {% endif %}
                    {% if Attr.filter_exclude %}
                    <span class="badge bg-red me-1 mb-1 text-wrap text-start" title="排除">
                      {{ Attr.filter_exclude }}
                    </span>
                    {% endif %}
                  </small>
              </div>
              <div class="text-muted">
                <small>
                  {% for Site in Attr.rss_sites %}
                  <span class="badge badge-outline text-azure me-1 mb-1" title="订阅站点">
                    {{ Site }}
                  </span>
                  {% endfor %}
                  {% for Site in Attr.search_sites %}
                  <span class="badge badge-outline text-teal me-1 mb-1" title="搜索站点">
                    {{ Site }}
                  </span>
                  {% endfor %}
                </small>
              </div>
            </div>
            <div class="space-between-box">
              <div>
                {% if Attr.state == 'D' %}
                <span class="badge bg-gray"></span> 队列中
                {% elif Attr.state == 'S' %}
                <span class="badge bg-orange"></span> 正在搜索
                {% elif Attr.state == 'R' %}
                <span class="badge bg-green"></span> 正在订阅
                {% if (Attr.total or -1) > 0 %}
                ({{ (Attr.total or -1) - (Attr.lack or -1) }}/{{ (Attr.total or -1) }})
                {% endif %}
                {% else %}
                <span class="badge bg-blue"></span> 完成
                {% endif %}
              </div>
              <div class="col-auto">
                <a href="javascript:show_edit_rss_media_modal('{{ Id }}', '{{TypeName}}')" title="编辑"
                  id="start_btn_{{ Id }}"><i class="ti ti-edit fs-2"></i></a>
                <a href="javascript:search_mediainfo_media('{{ Attr.tmdbid }}','{{ Attr.name }}', '{{TypeName}}')"
                  title="搜索" id="stop_btn_{{ Id }}"><i class="ti ti-search fs-2"></i></a>
                <a href="javascript:refresh_rss_media('{{Type}}', '{{ Id }}', 'movie_rss')" title="刷新"
                  id="stop_btn_{{ Id }}"><i class="ti ti-refresh fs-2"></i></a>
              </div>
            </div>
          </div>
        </div>
      </div>
      {% if Type == 'TV' %}
      <div class="card-progress" style="overflow: hidden">
        <div class="progress-bar bg-green"
          style="width:{% if (Attr.total or -1) > 0 %}{{ ((Attr.total or -1)-(Attr.lack or -1))*100/(Attr.total or -1) }}{% endif %}%"
          role="progressbar"
          aria-valuenow="{% if (Attr.total or -1) > 0 %}{{ ((Attr.total or -1)-(Attr.lack or -1))*100/(Attr.total or -1) }}{% endif %}"
          aria-valuemin="0" aria-valuemax="100">
        </div>
      </div>
      {% endif %}
    </div>
    {% endfor %}
  </div>
</div>
</div>
{% else %}
  {% if Type == 'TV' %}
  <nodata-found title="没有订阅" text="当前没有正在订阅的电视剧"></nodata-found>
  {% else %}
  <nodata-found title="没有订阅" text="当前没有正在订阅的电影"></nodata-found>
  {% endif %}
{% endif %}