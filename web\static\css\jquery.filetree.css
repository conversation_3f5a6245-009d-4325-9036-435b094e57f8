UL.jqueryFileTree{padding:5px;margin:0px;display:none;line-height:150%;}
UL.jqueryFileTree LI{list-style:none;padding:0px;padding-left:20px;margin:0px;white-space:nowrap;}
UL.jqueryFileTree A{color:inherit;text-decoration:none;display:inline-block;padding:0px 2px;cursor:pointer;}
UL.jqueryFileTree A:hover{background:#90b5e2;}
/* Core Styles */
.jqueryFileTree LI.directory{background:url(../img/filetree/directory.png) left top no-repeat;white-space:normal!important;word-break:break-all;}
.jqueryFileTree LI.expanded{background:url(../img/filetree/folder_open.png) left top no-repeat;}
.jqueryFileTree LI.expanded > a{font-weight: bold;}
.jqueryFileTree LI.file{background:url(../img/filetree/file.png) left top no-repeat;}
.jqueryFileTree LI.wait{background:url(../img/filetree/spinner.gif) left top no-repeat;}
/* File Extensions*/
.jqueryFileTree LI.ext_3gp{background:url(../img/filetree/film.png) left top no-repeat;}
.jqueryFileTree LI.ext_afp{background:url(../img/filetree/code.png) left top no-repeat;}
.jqueryFileTree LI.ext_afpa{background:url(../img/filetree/code.png) left top no-repeat;}
.jqueryFileTree LI.ext_asp{background:url(../img/filetree/code.png) left top no-repeat;}
.jqueryFileTree LI.ext_aspx{background:url(../img/filetree/code.png) left top no-repeat;}
.jqueryFileTree LI.ext_avi{background:url(../img/filetree/film.png) left top no-repeat;}
.jqueryFileTree LI.ext_bat{background:url(../img/filetree/application.png) left top no-repeat;}
.jqueryFileTree LI.ext_bmp{background:url(../img/filetree/picture.png) left top no-repeat;}
.jqueryFileTree LI.ext_c{background:url(../img/filetree/code.png) left top no-repeat;}
.jqueryFileTree LI.ext_cfm{background:url(../img/filetree/code.png) left top no-repeat;}
.jqueryFileTree LI.ext_cgi{background:url(../img/filetree/code.png) left top no-repeat;}
.jqueryFileTree LI.ext_com{background:url(../img/filetree/application.png) left top no-repeat;}
.jqueryFileTree LI.ext_cpp{background:url(../img/filetree/code.png) left top no-repeat;}
.jqueryFileTree LI.ext_css{background:url(../img/filetree/css.png) left top no-repeat;}
.jqueryFileTree LI.ext_doc{background:url(../img/filetree/doc.png) left top no-repeat;}
.jqueryFileTree LI.ext_exe{background:url(../img/filetree/application.png) left top no-repeat;}
.jqueryFileTree LI.ext_gif{background:url(../img/filetree/picture.png) left top no-repeat;}
.jqueryFileTree LI.ext_fla{background:url(../img/filetree/swf.png) left top no-repeat;}
.jqueryFileTree LI.ext_h{background:url(../img/filetree/code.png) left top no-repeat;}
.jqueryFileTree LI.ext_htm{background:url(../img/filetree/html.png) left top no-repeat;}
.jqueryFileTree LI.ext_html{background:url(../img/filetree/html.png) left top no-repeat;}
.jqueryFileTree LI.ext_img{background:url(../img/filetree/disk-image.png) left top no-repeat;}
.jqueryFileTree LI.ext_iso{background:url(../img/filetree/cdrom.png) left top no-repeat;}
.jqueryFileTree LI.ext_jar{background:url(../img/filetree/java.png) left top no-repeat;}
.jqueryFileTree LI.ext_jpg{background:url(../img/filetree/picture.png) left top no-repeat;}
.jqueryFileTree LI.ext_jpeg{background:url(../img/filetree/picture.png) left top no-repeat;}
.jqueryFileTree LI.ext_js{background:url(../img/filetree/script.png) left top no-repeat;}
.jqueryFileTree LI.ext_lasso{background:url(../img/filetree/code.png) left top no-repeat;}
.jqueryFileTree LI.ext_log{background:url(../img/filetree/txt.png) left top no-repeat;}
.jqueryFileTree LI.ext_m4p{background:url(../img/filetree/music.png) left top no-repeat;}
.jqueryFileTree LI.ext_mov{background:url(../img/filetree/film.png) left top no-repeat;}
.jqueryFileTree LI.ext_mp3{background:url(../img/filetree/music.png) left top no-repeat;}
.jqueryFileTree LI.ext_mp4{background:url(../img/filetree/film.png) left top no-repeat;}
.jqueryFileTree LI.ext_mpg{background:url(../img/filetree/film.png) left top no-repeat;}
.jqueryFileTree LI.ext_mpeg{background:url(../img/filetree/film.png) left top no-repeat;}
.jqueryFileTree LI.ext_ogg{background:url(../img/filetree/music.png) left top no-repeat;}
.jqueryFileTree LI.ext_pcx{background:url(../img/filetree/picture.png) left top no-repeat;}
.jqueryFileTree LI.ext_pdf{background:url(../img/filetree/pdf.png) left top no-repeat;}
.jqueryFileTree LI.ext_php{background:url(../img/filetree/php.png) left top no-repeat;}
.jqueryFileTree LI.ext_plg{background:url(../img/filetree/plg.png) left top no-repeat;}
.jqueryFileTree LI.ext_png{background:url(../img/filetree/picture.png) left top no-repeat;}
.jqueryFileTree LI.ext_ppt{background:url(../img/filetree/ppt.png) left top no-repeat;}
.jqueryFileTree LI.ext_psd{background:url(../img/filetree/psd.png) left top no-repeat;}
.jqueryFileTree LI.ext_pl{background:url(../img/filetree/script.png) left top no-repeat;}
.jqueryFileTree LI.ext_py{background:url(../img/filetree/script.png) left top no-repeat;}
.jqueryFileTree LI.ext_qcow{background:url(../img/filetree/disk-image.png) left top no-repeat;}
.jqueryFileTree LI.ext_qcow2{background:url(../img/filetree/disk-image.png) left top no-repeat;}
.jqueryFileTree LI.ext_rb{background:url(../img/filetree/ruby.png) left top no-repeat;}
.jqueryFileTree LI.ext_rbx{background:url(../img/filetree/ruby.png) left top no-repeat;}
.jqueryFileTree LI.ext_rhtml{background:url(../img/filetree/ruby.png) left top no-repeat;}
.jqueryFileTree LI.ext_rpm{background:url(../img/filetree/linux.png) left top no-repeat;}
.jqueryFileTree LI.ext_ruby{background:url(../img/filetree/ruby.png) left top no-repeat;}
.jqueryFileTree LI.ext_sql{background:url(../img/filetree/db.png) left top no-repeat;}
.jqueryFileTree LI.ext_swf{background:url(../img/filetree/swf.png) left top no-repeat;}
.jqueryFileTree LI.ext_tif{background:url(../img/filetree/picture.png) left top no-repeat;}
.jqueryFileTree LI.ext_tiff{background:url(../img/filetree/picture.png) left top no-repeat;}
.jqueryFileTree LI.ext_txt{background:url(../img/filetree/txt.png) left top no-repeat;}
.jqueryFileTree LI.ext_vb{background:url(../img/filetree/code.png) left top no-repeat;}
.jqueryFileTree LI.ext_wav{background:url(../img/filetree/music.png) left top no-repeat;}
.jqueryFileTree LI.ext_wmv{background:url(../img/filetree/film.png) left top no-repeat;}
.jqueryFileTree LI.ext_xls{background:url(../img/filetree/xls.png) left top no-repeat;}
.jqueryFileTree LI.ext_xml{background:url(../img/filetree/code.png) left top no-repeat;}
.jqueryFileTree LI.ext_zip{background:url(../img/filetree/zip.png) left top no-repeat;}
