<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航函数测试</title>
    
    <!-- 本地CSS库 -->
    <link href="./libs/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="./libs/tabler/css/tabler.min.css" rel="stylesheet">
    <link href="./libs/nprogress.min.css" rel="stylesheet">
    
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        
        .status-item {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 0.375rem;
            border: 1px solid #dee2e6;
        }
        
        .status-success {
            background-color: #d1e7dd;
            color: #0f5132;
            border-color: #badbcc;
        }
        
        .status-error {
            background-color: #f8d7da;
            color: #842029;
            border-color: #f5c2c7;
        }
        
        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border-color: #b8daff;
        }
        
        .function-test {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>导航函数测试</h1>
        <p>此页面用于测试 window.navigateTo 函数是否正确定义和工作</p>
        
        <!-- 测试结果 -->
        <div id="test_results">
            <h3>测试结果</h3>
            <div id="status_list"></div>
            
            <!-- 函数测试 -->
            <div class="function-test">
                <h4>函数可用性测试</h4>
                <div class="btn-group mb-3" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="testNavigateToFunction()">测试 navigateTo 函数</button>
                    <button type="button" class="btn btn-outline-secondary" onclick="testNavmenuFunction()">测试 navmenu 函数</button>
                    <button type="button" class="btn btn-outline-info" onclick="testLoadPageContentFunction()">测试 loadPageContent 函数</button>
                </div>
                <div id="function_test_results"></div>
            </div>
            
            <!-- 导航测试 -->
            <div class="function-test">
                <h4>导航功能测试</h4>
                <div class="btn-group mb-3" role="group">
                    <button type="button" class="btn btn-primary" onclick="testNavigation('index')">导航到首页</button>
                    <button type="button" class="btn btn-primary" onclick="testNavigation('search')">导航到搜索</button>
                    <button type="button" class="btn btn-primary" onclick="testNavigation('ranking')">导航到排行</button>
                </div>
                <div id="navigation_test_results"></div>
            </div>
            
            <!-- 页面内容显示区域 -->
            <div class="function-test">
                <h4>页面内容</h4>
                <div id="page_content" style="min-height: 200px; background: #f8f9fa; padding: 1rem; border-radius: 0.375rem;">
                    <p>页面内容将显示在这里</p>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript库 - 按照index.html中的顺序加载 -->
    <script src="./js/jquery-3.3.1.min.js"></script>
    <script src="./libs/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="./libs/tabler/js/tabler.min.js"></script>
    <script src="./libs/navigo.min.js"></script>
    <script src="./libs/nprogress.min.js"></script>
    <script src="./js/axios.min.js"></script>
    <script src="./js/auth-manager.js"></script>
    <script src="./js/axios-wrapper.js"></script>
    <script src="./js/util.js"></script>
    <script src="./js/functions.js"></script>
    <script src="./js/app.js"></script>

    <!-- 全局导航函数定义 - 与index.html中相同 -->
    <script>
        // 全局导航函数，供组件使用
        window.navigateTo = function(page) {
            addTestResult('navigateTo调用', 'info', `尝试导航到页面: ${page}`);
            
            // 直接使用路由导航，避免循环调用
            if (window.appRouter) {
                if (!page.startsWith('/')) {
                    page = '/' + page;
                }
                addTestResult('路由导航', 'success', `使用appRouter导航到: ${page}`);
                window.appRouter.navigate(page);
            } else if (typeof loadPageContent === 'function') {
                addTestResult('页面加载', 'success', `使用loadPageContent加载: ${page}`);
                loadPageContent(page);
            } else {
                // 备用方案：直接调用原始的navmenu逻辑
                addTestResult('备用导航', 'info', `使用备用方案导航到: ${page}`);
                
                // 修复空格问题
                page = page.replaceAll(" ", "%20");
                
                // 显示进度条
                if (typeof NProgress !== 'undefined') {
                    NProgress.start();
                }
                
                // 模拟页面内容加载
                $("#page_content").html(`
                    <div class="alert alert-info">
                        <h5>模拟页面: ${page}</h5>
                        <p>这是一个测试页面，用于验证导航功能。</p>
                        <p>时间: ${new Date().toLocaleString()}</p>
                    </div>
                `);
                
                if (typeof NProgress !== 'undefined') {
                    NProgress.done();
                }
                
                addTestResult('备用导航', 'success', `成功加载页面: ${page}`);
            }
        };
    </script>

    <script>
        // 测试状态
        let testResults = [];
        
        // 添加测试结果
        function addTestResult(name, status, message) {
            testResults.push({ name, status, message, time: new Date().toLocaleTimeString() });
            updateStatusDisplay();
        }
        
        // 更新状态显示
        function updateStatusDisplay() {
            const statusList = document.getElementById('status_list');
            statusList.innerHTML = '';
            
            testResults.forEach(result => {
                const div = document.createElement('div');
                div.className = `status-item status-${result.status}`;
                div.innerHTML = `<strong>[${result.time}] ${result.name}:</strong> ${result.message}`;
                statusList.appendChild(div);
            });
            
            // 滚动到最新结果
            statusList.scrollTop = statusList.scrollHeight;
        }
        
        // 测试 navigateTo 函数
        function testNavigateToFunction() {
            const results = document.getElementById('function_test_results');
            
            if (typeof window.navigateTo === 'function') {
                results.innerHTML = '<div class="alert alert-success">✅ window.navigateTo 函数已定义</div>';
                addTestResult('函数检查', 'success', 'window.navigateTo 函数可用');
            } else {
                results.innerHTML = '<div class="alert alert-danger">❌ window.navigateTo 函数未定义</div>';
                addTestResult('函数检查', 'error', 'window.navigateTo 函数不可用');
            }
        }
        
        // 测试 navmenu 函数
        function testNavmenuFunction() {
            const results = document.getElementById('function_test_results');
            
            if (typeof navmenu === 'function') {
                results.innerHTML += '<div class="alert alert-success">✅ navmenu 函数已定义</div>';
                addTestResult('函数检查', 'success', 'navmenu 函数可用');
            } else {
                results.innerHTML += '<div class="alert alert-warning">⚠️ navmenu 函数未定义</div>';
                addTestResult('函数检查', 'info', 'navmenu 函数不可用');
            }
        }
        
        // 测试 loadPageContent 函数
        function testLoadPageContentFunction() {
            const results = document.getElementById('function_test_results');
            
            if (typeof loadPageContent === 'function') {
                results.innerHTML += '<div class="alert alert-success">✅ loadPageContent 函数已定义</div>';
                addTestResult('函数检查', 'success', 'loadPageContent 函数可用');
            } else {
                results.innerHTML += '<div class="alert alert-warning">⚠️ loadPageContent 函数未定义</div>';
                addTestResult('函数检查', 'info', 'loadPageContent 函数不可用');
            }
        }
        
        // 测试导航功能
        function testNavigation(page) {
            const results = document.getElementById('navigation_test_results');
            results.innerHTML = `<div class="alert alert-info">正在测试导航到: ${page}</div>`;
            
            try {
                window.navigateTo(page);
                results.innerHTML += `<div class="alert alert-success">导航测试完成: ${page}</div>`;
            } catch (error) {
                results.innerHTML += `<div class="alert alert-danger">导航测试失败: ${error.message}</div>`;
                addTestResult('导航测试', 'error', `导航失败: ${error.message}`);
            }
        }
        
        // 页面加载完成后开始测试
        $(document).ready(function() {
            addTestResult('页面加载', 'success', 'jQuery和基础库加载完成');
            
            // 检查必要的库
            const libraries = [
                { name: 'jQuery', check: () => typeof $ !== 'undefined' },
                { name: 'Navigo', check: () => typeof Navigo !== 'undefined' },
                { name: 'NProgress', check: () => typeof NProgress !== 'undefined' },
                { name: 'Bootstrap', check: () => typeof bootstrap !== 'undefined' }
            ];
            
            libraries.forEach(lib => {
                if (lib.check()) {
                    addTestResult('库检查', 'success', `${lib.name} 加载成功`);
                } else {
                    addTestResult('库检查', 'error', `${lib.name} 未加载`);
                }
            });
            
            // 自动测试函数可用性
            setTimeout(() => {
                testNavigateToFunction();
                testNavmenuFunction();
                testLoadPageContentFunction();
            }, 1000);
        });
    </script>
</body>
</html>
