import base64

from app.utils import RequestUtils
from config import Config


class OcrHelper:

    _ocr_b64_url = "https://ocr.ddsrem.com/captcha/base64"

    def get_captcha_text(self, image_url=None, image_b64=None, cookie=None, ua=None):
        """
        根据图片地址，获取验证码图片，并识别内容
        :param image_url: 图片地址
        :param image_b64: 图片base64，跳过图片地址下载
        :param cookie: 下载图片使用的cookie
        :param ua: 下载图片使用的ua
        """
        ocr_url = Config().get_config("app").get("ocr_url")
        if ocr_url:
            self._ocr_b64_url = ocr_url
        if image_url:
            ret = RequestUtils(ua=ua,
                               cookies=cookie).get_res(image_url)
            if ret is not None:
                image_bin = ret.content
                if not image_bin:
                    return ""
                image_b64 = base64.b64encode(image_bin).decode()
        if not image_b64:
            return ""
        ret = RequestUtils(content_type="application/json").post_res(
            url=self._ocr_b64_url,
            json={"base64_img": image_b64})
        if ret:
            return ret.json().get("result")
        return ""
