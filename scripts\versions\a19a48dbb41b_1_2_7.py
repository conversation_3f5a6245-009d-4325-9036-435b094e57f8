"""1.2.7

Revision ID: a19a48dbb41b
Revises: d68a85a8f10d
Create Date: 2023-05-09 14:44:03.251571

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a19a48dbb41b'
down_revision = 'd68a85a8f10d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    try:
        op.create_index(op.f('ix_TRANSFER_HISTORY_DATE'), 'TRANSFER_HISTORY', ['DATE'], unique=False)
    except Exception as e:
        pass
    # ### end Alembic commands ###


def downgrade() -> None:
    pass
