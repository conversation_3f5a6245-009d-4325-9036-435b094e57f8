// Token管理
function getToken() {
  return localStorage.getItem("access_token") || document.cookie.match(/access_token=([^;]+)/)?.[1];
}

function clearToken() {
  localStorage.removeItem("access_token");
  document.cookie = "access_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
}

// 全局变量
let NavPageXhr = null;
let NavPageLoading = false;
let CurrentPageUri = "";
let menuData = [];

// 页面初始化时检查 token
if (!getToken()) {
  window.location.href = "/login";
}

// 加载菜单数据
async function loadMenuData() {
  try {
    const response = await fetch('/static/config/menu.json');
    if (response.ok) {
      menuData = await response.json();
    } else {
      console.error('Failed to load menu data');
    }
  } catch (error) {
    console.error('Error loading menu data:', error);
  }
}

// 显示加载动画
function showLoading() {
  $("#loading_tips").show();
  $("#page_content").hide();
  if (typeof NProgress !== 'undefined') {
    NProgress.start();
  }
}

// 隐藏加载动画
function hideLoading() {
  $("#loading_tips").hide();
  $("#page_content").show();
  if (typeof NProgress !== 'undefined') {
    NProgress.done();
  }
}

// 页面加载函数 - 替代原有的navmenu函数
function loadPage(page, params = {}) {
  if (!getToken()) {
    window.location.href = "/login";
    return;
  }

  // 处理空格问题
  page = page.replace(/ /g, "%20");

  // 清除页码
  sessionStorage.removeItem("CurrentPage");

  // 停止上一次加载
  if (NavPageXhr && NavPageLoading) {
    NavPageXhr.abort();
  }

  // 显示加载动画
  showLoading();

  // 构建请求URL和参数
  let requestUrl = `/data/${page}`;
  let queryString = new URLSearchParams(params).toString();
  if (queryString) {
    requestUrl += `?${queryString}`;
  }

  NavPageLoading = true;

  // 使用fetch API替代jQuery ajax
  fetch(requestUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${getToken()}`
    },
    body: JSON.stringify(params)
  })
  .then(response => {
    if (response.status === 401) {
      // Token过期，重定向到登录页
      clearToken();
      window.location.href = "/login";
      return;
    }
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    // 渲染页面内容
    renderPageContent(page, data);

    // 加载完成
    NavPageLoading = false;
    hideLoading();

    // 更新当前页面URI
    CurrentPageUri = page;

    // 滚动到顶部
    $(window).scrollTop(0);

    // 更新菜单状态
    updateMenuState(page);

    // 刷新tooltip等UI组件
    if (typeof fresh_tooltip === 'function') {
      fresh_tooltip();
    }
    if (typeof init_filetree_element === 'function') {
      init_filetree_element();
    }
  })
  .catch(error => {
    console.error('Error loading page:', error);
    $("#page_content").html(`<div class="alert alert-danger">加载页面失败: ${error.message}</div>`);
    NavPageLoading = false;
    hideLoading();
  });
}

// 渲染页面内容
function renderPageContent(page, data) {
  // 根据页面类型渲染不同的内容
  let content = '';

  switch (page) {
    case 'index':
      content = renderIndexPage(data);
      break;
    case 'search':
      content = renderSearchPage(data);
      break;
    case 'ranking':
      content = renderRankingPage(data);
      break;
    case 'tv_ranking':
      content = renderTvRankingPage(data);
      break;
    case 'bangumi':
      content = renderBangumiPage(data);
      break;
    case 'movie_rss':
      content = renderMovieRssPage(data);
      break;
    case 'tv_rss':
      content = renderTvRssPage(data);
      break;
    case 'user_rss':
      content = renderUserRssPage(data);
      break;
    case 'rss_calendar':
      content = renderRssCalendarPage(data);
      break;
    case 'service':
      content = renderServicePage(data);
      break;
    case 'downloading':
      content = renderDownloadingPage(data);
      break;
    case 'mediafile':
      content = renderMediafilePage(data);
      break;
    case 'torrent_remove':
      content = renderTorrentRemovePage(data);
      break;
    case 'history':
      content = renderHistoryPage(data);
      break;
    case 'unidentification':
      content = renderUnidentificationPage(data);
      break;
    case 'tmdbcache':
      content = renderTmdbcachePage(data);
      break;
    case 'basic':
      content = renderBasicPage(data);
      break;
    case 'library':
      content = renderLibraryPage(data);
      break;
    case 'notification':
      content = renderNotificationPage(data);
      break;
    case 'directorysync':
      content = renderDirectorysyncPage(data);
      break;
    case 'filterrule':
      content = renderFilterrulePage(data);
      break;
    case 'customwords':
      content = renderCustomwordsPage(data);
      break;
    case 'indexer':
      content = renderIndexerPage(data);
      break;
    case 'ptindexer':
      content = renderPtindexerPage(data);
      break;
    case 'site':
      content = renderSitePage(data);
      break;
    case 'statistics':
      content = renderStatisticsPage(data);
      break;
    case 'brushtask':
      content = renderBrushtaskPage(data);
      break;
    case 'plugin':
      content = renderPluginPage(data);
      break;
    case 'users':
      content = renderUsersPage(data);
      break;
    default:
      content = renderDefaultPage(page, data);
      break;
  }

  $("#page_content").html(content);
}

// 获取页面标题
function getPageTitle(page) {
  const menuItem = findMenuItemByPage(page);
  return menuItem ? menuItem.name : page;
}

// 在菜单数据中查找页面
function findMenuItemByPage(page) {
  for (const menu of menuData) {
    if (menu.page === page) {
      return menu;
    }
    if (menu.nav) {
      for (const subMenu of menu.nav) {
        if (subMenu.page === page) {
          return subMenu;
        }
      }
    }
  }
  return null;
}

// 更新菜单状态
function updateMenuState(page) {
  // 更新导航菜单的激活状态
  const navbarMenu = document.querySelector("#navbar-menu");
  if (navbarMenu && navbarMenu.update_active) {
    navbarMenu.update_active(page);
  }

  // 处理子菜单显示
  updateSubMenu(page);
}

// 更新子菜单
function updateSubMenu(page) {
  const parentMenu = findParentMenu(page);

  if (parentMenu && parentMenu.nav) {
    // 显示子菜单
    const ulElement = document.querySelector('#top-sub-navbar ul');
    if (ulElement) {
      ulElement.innerHTML = '';

      parentMenu.nav.forEach(item => {
        const aElement = document.createElement('a');
        aElement.className = `nav-link top-nav-link ${item.page === page ? 'active' : ''}`;
        aElement.href = '#';
        aElement.innerHTML = `
          <span class="tab-icon" style="color:var(--tblr-body-color);display: inline-flex;align-items: center;">
            ${item.icon}
          </span>
          <span class="tab-text">${item.name}</span>
        `;
        aElement.setAttribute('data-bs-toggle', 'tab');
        aElement.setAttribute('data-id', item.page);
        aElement.onclick = () => router.navigate(`/${item.page}`);

        const liElement = document.createElement('li');
        liElement.className = 'nav-item';
        liElement.appendChild(aElement);
        ulElement.appendChild(liElement);
      });

      $('#top-sub-navbar').show();
    }
  } else {
    $('#top-sub-navbar').hide();
  }
}

// 查找父菜单
function findParentMenu(page) {
  for (const menu of menuData) {
    if (menu.nav) {
      for (const subMenu of menu.nav) {
        if (subMenu.page === page) {
          return menu;
        }
      }
    }
  }
  return null;
}

// 初始化路由
const router = new Navigo("/", { hash: true });

// 动态生成路由配置
async function initializeRoutes() {
  await loadMenuData();

  // 收集所有页面名称
  const allPages = new Set();

  menuData.forEach(menu => {
    if (menu.page) {
      allPages.add(menu.page);
    }
    if (menu.nav) {
      menu.nav.forEach(subMenu => {
        if (subMenu.page) {
          allPages.add(subMenu.page);
        }
      });
    }
  });

  // 为每个页面创建路由
  allPages.forEach(page => {
    // 基本路由
    router.on(`/${page}`, function () {
      loadPage(page, {});
    });

    // 带查询参数的路由 (例如: /search?s=keyword)
    router.on(`/${page}`, function () {
      const urlParams = new URLSearchParams(window.location.search);
      const params = {};
      for (const [key, value] of urlParams) {
        params[key] = value;
      }
      loadPage(page, params);
    });
  });

  // 默认路由 - 重定向到首页
  router.on("/", function () {
    router.navigate("/index");
  });

  // 根路径处理
  router.on("", function () {
    router.navigate("/index");
  });

  // 404处理
  router.notFound(() => {
    $("#page_content").html(`
      <div class="container-xl">
        <div class="empty">
          <div class="empty-img">
            <img src="/static/img/undraw_page_not_found_su7k.svg" height="128" alt="">
          </div>
          <p class="empty-title">页面不存在</p>
          <p class="empty-subtitle text-muted">
            您访问的页面不存在或已被删除
          </p>
          <div class="empty-action">
            <a href="#/index" class="btn btn-primary">
              <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                <line x1="5" y1="12" x2="19" y2="12" />
                <line x1="5" y1="12" x2="11" y2="18" />
                <line x1="5" y1="12" x2="11" y2="6" />
              </svg>
              返回首页
            </a>
          </div>
        </div>
      </div>
    `);
  });

  // 启动路由
  router.resolve();
}

// 页面历史管理
function updateHistory(page, params = {}) {
  const state = {
    page: page,
    params: params,
    timestamp: Date.now()
  };

  const url = `#/${page}`;
  window.history.pushState(state, "", url);
}

// 处理浏览器后退
window.addEventListener('popstate', function (event) {
  if (event.state && event.state.page) {
    loadPage(event.state.page, event.state.params || {});
  }
});

// 退出登录处理
$(document).on("click", "#logoutBtn", function () {
  clearToken();
  window.location.href = "/login";
});

// 页面渲染函数
function renderIndexPage(data) {
  return `
    <div class="container-xl">
      <div class="page-header d-print-none">
        <div class="row align-items-center">
          <div class="col">
            <h2 class="page-title">我的媒体库</h2>
          </div>
          <div class="col-auto ms-auto d-print-none">
            <div class="btn-list">
              <button class="btn btn-primary" onclick="syncMediaLibrary()">
                <i class="ti ti-refresh fs-2"></i>
                媒体库同步
              </button>
              <button class="btn btn-cyan" onclick="showStatistics()">
                <i class="ti ti-chart-pie fs-2"></i>
                统计数据
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="page-body">
        <div class="container-xl">
          <div class="row">
            <div class="col-12">
              <div class="card">
                <div class="card-body">
                  <h3>媒体库概览</h3>
                  <p>媒体服务器状态: ${data.ServerSucess ? '正常' : '异常'}</p>
                  ${data.MediaCounts ? `
                    <div class="row">
                      <div class="col-md-3">
                        <div class="card">
                          <div class="card-body text-center">
                            <h4>${data.MediaCounts.MovieCount || 0}</h4>
                            <p>电影</p>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-3">
                        <div class="card">
                          <div class="card-body text-center">
                            <h4>${data.MediaCounts.SeriesCount || 0}</h4>
                            <p>电视剧</p>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-3">
                        <div class="card">
                          <div class="card-body text-center">
                            <h4>${data.MediaCounts.EpisodeCount || 0}</h4>
                            <p>剧集</p>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-3">
                        <div class="card">
                          <div class="card-body text-center">
                            <h4>${data.MediaCounts.SongCount || 0}</h4>
                            <p>音乐</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ` : ''}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

function renderSearchPage(data) {
  const searchResults = data.results || [];
  return `
    <div class="container-xl">
      <div class="page-header d-print-none">
        <div class="row align-items-center">
          <div class="col">
            <h2 class="page-title">资源搜索</h2>
          </div>
        </div>
      </div>
      <div class="page-body">
        <div class="container-xl">
          <div class="row">
            <div class="col-12">
              <div class="card">
                <div class="card-body">
                  <div class="mb-3">
                    <label class="form-label">搜索关键词</label>
                    <div class="input-group">
                      <input type="text" class="form-control" id="search-input" placeholder="请输入电影或电视剧名称">
                      <button class="btn btn-primary" onclick="performSearch()">搜索</button>
                    </div>
                  </div>
                  <div id="search-results">
                    ${searchResults.length > 0 ? `
                      <div class="row">
                        ${searchResults.map(result => `
                          <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card">
                              <div class="card-body">
                                <h5>${result.title}</h5>
                                <p>${result.description || ''}</p>
                              </div>
                            </div>
                          </div>
                        `).join('')}
                      </div>
                    ` : '<p>暂无搜索结果</p>'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

function renderUsersPage(data) {
  const users = data.Users || [];
  return `
    <div class="container-xl">
      <div class="page-header d-print-none">
        <div class="row align-items-center">
          <div class="col">
            <h2 class="page-title">用户管理</h2>
          </div>
          <div class="col-auto ms-auto d-print-none">
            <button class="btn btn-primary" onclick="addUser()">
              <i class="ti ti-plus fs-2"></i>
              添加用户
            </button>
          </div>
        </div>
      </div>
      <div class="page-body">
        <div class="container-xl">
          <div class="row">
            <div class="col-12">
              <div class="card">
                <div class="table-responsive">
                  <table class="table table-vcenter card-table">
                    <thead>
                      <tr>
                        <th>用户名</th>
                        <th>权限</th>
                        <th>状态</th>
                        <th>操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      ${users.map(user => `
                        <tr>
                          <td>${user.name}</td>
                          <td>${user.pris}</td>
                          <td>
                            <span class="badge bg-${user.level > 0 ? 'success' : 'secondary'}">
                              ${user.level > 0 ? '启用' : '禁用'}
                            </span>
                          </td>
                          <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="editUser(${user.id})">编辑</button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(${user.id})">删除</button>
                          </td>
                        </tr>
                      `).join('')}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

function renderDefaultPage(page, data) {
  return `
    <div class="container-xl">
      <div class="page-header d-print-none">
        <div class="row align-items-center">
          <div class="col">
            <h2 class="page-title">${getPageTitle(page)}</h2>
          </div>
        </div>
      </div>
      <div class="page-body">
        <div class="container-xl">
          <div class="row">
            <div class="col-12">
              <div class="card">
                <div class="card-body">
                  <p>页面正在开发中...</p>
                  <details>
                    <summary>调试信息</summary>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                  </details>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

// 其他页面渲染函数的占位符
function renderRankingPage(data) { return renderDefaultPage('ranking', data); }
function renderTvRankingPage(data) { return renderDefaultPage('tv_ranking', data); }
function renderBangumiPage(data) { return renderDefaultPage('bangumi', data); }
function renderMovieRssPage(data) { return renderDefaultPage('movie_rss', data); }
function renderTvRssPage(data) { return renderDefaultPage('tv_rss', data); }
function renderUserRssPage(data) { return renderDefaultPage('user_rss', data); }
function renderRssCalendarPage(data) { return renderDefaultPage('rss_calendar', data); }
function renderServicePage(data) { return renderDefaultPage('service', data); }
function renderDownloadingPage(data) { return renderDefaultPage('downloading', data); }
function renderMediafilePage(data) { return renderDefaultPage('mediafile', data); }
function renderTorrentRemovePage(data) { return renderDefaultPage('torrent_remove', data); }
function renderHistoryPage(data) { return renderDefaultPage('history', data); }
function renderUnidentificationPage(data) { return renderDefaultPage('unidentification', data); }
function renderTmdbcachePage(data) { return renderDefaultPage('tmdbcache', data); }
function renderBasicPage(data) { return renderDefaultPage('basic', data); }
function renderLibraryPage(data) { return renderDefaultPage('library', data); }
function renderNotificationPage(data) { return renderDefaultPage('notification', data); }
function renderDirectorysyncPage(data) { return renderDefaultPage('directorysync', data); }
function renderFilterrulePage(data) { return renderDefaultPage('filterrule', data); }
function renderCustomwordsPage(data) { return renderDefaultPage('customwords', data); }
function renderIndexerPage(data) { return renderDefaultPage('indexer', data); }
function renderPtindexerPage(data) { return renderDefaultPage('ptindexer', data); }
function renderSitePage(data) { return renderDefaultPage('site', data); }
function renderStatisticsPage(data) { return renderDefaultPage('statistics', data); }
function renderBrushtaskPage(data) { return renderDefaultPage('brushtask', data); }
function renderPluginPage(data) { return renderDefaultPage('plugin', data); }

// 初始化应用
$(document).ready(function() {
  initializeRoutes();
});
