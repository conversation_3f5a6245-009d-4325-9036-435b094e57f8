function getToken() {
  return localStorage.getItem("jwt_token");
}

function clearToken() {
  localStorage.removeItem("jwt_token");
}

$("#logoutBtn").on("click", function () {
  clearToken();
  window.location.href = "/login.html";
});

// 页面初始化时检查 token
if (!getToken()) {
  window.location.href = "/login.html";
}

// 初始化路由
const router = new Navigo("/", { hash: true });

router
  // 首页
  .on("/index", function () {
    loadPage("index");
  })
  .on("/", function () {
    router.navigate("/index");
  })

  // 探索模块
  .on("/ranking", function () {
    loadPage("ranking");
  })
  .on("/tv_ranking", function () {
    loadPage("tv_ranking");
  })
  .on("/bangumi", function () {
    loadPage("bangumi");
  })

  // 资源搜索
  .on("/search", function () {
    loadPage("search");
  })

  // 订阅模块
  .on("/movie_rss", function () {
    loadPage("movie_rss");
  })
  .on("/tv_rss", function () {
    loadPage("tv_rss");
  })
  .on("/user_rss", function () {
    loadPage("user_rss");
  })
  .on("/rss_calendar", function () {
    loadPage("rss_calendar");
  })

  // 服务
  .on("/service", function () {
    loadPage("service");
  })

  // 下载管理
  .on("/downloading", function () {
    loadPage("downloading");
  })

  // 文件管理
  .on("/mediafile", function () {
    loadPage("mediafile");
  })
  .on("/torrent_remove", function () {
    loadPage("torrent_remove");
  })
  .on("/history", function () {
    loadPage("history");
  })
  .on("/unidentification", function () {
    loadPage("unidentification");
  })
  .on("/tmdbcache", function () {
    loadPage("tmdbcache");
  })

  // 系统设置
  .on("/basic", function () {
    loadPage("basic");
  })
  .on("/library", function () {
    loadPage("library");
  })
  .on("/notification", function () {
    loadPage("notification");
  })
  .on("/directorysync", function () {
    loadPage("directorysync");
  })
  .on("/filterrule", function () {
    loadPage("filterrule");
  })
  .on("/customwords", function () {
    loadPage("customwords");
  })

  // 站点管理
  .on("/indexer", function () {
    loadPage("indexer");
  })
  .on("/ptindexer", function () {
    loadPage("ptindexer");
  })
  .on("/site", function () {
    loadPage("site");
  })
  .on("/statistics", function () {
    loadPage("statistics");
  })
  .on("/brushtask", function () {
    loadPage("brushtask");
  })

  // 插件管理
  .on("/plugin", function () {
    loadPage("plugin");
  })

  // 用户管理
  .on("/users", function () {
    loadPage("users");
  })

  // 404处理
  .notFound(() => {
    $("#page_content").html("<h3>404 页面不存在</h3>");
  });

function loadPage(url) {
  if (!getToken()) {
    window.location.href = "/login.html";
    return;
  }
  $("#page_content").load(url);
}

router.resolve();
