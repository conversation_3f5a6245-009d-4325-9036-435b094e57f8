function getToken() {
  return localStorage.getItem("jwt_token");
}

function clearToken() {
  localStorage.removeItem("jwt_token");
}

$("#logoutBtn").on("click", function () {
  clearToken();
  window.location.href = "/login.html";
});

// 页面初始化时检查 token
if (!getToken()) {
  window.location.href = "/login.html";
}

// 初始化路由
const router = new Navigo("/", { hash: true });

router
  .on("/users", function () {
    loadPage("/static/js/pages/users.html");
  })
  .on("/orders", function () {
    loadPage("/static/js/pages/orders.html");
  })
  .notFound(() => {
    $("#page_content").html("<h3>404 页面不存在</h3>");
  });

function loadPage(url) {
  if (!getToken()) {
    window.location.href = "/login.html";
    return;
  }
  $("#page_content").load(url);
}

router.resolve();
