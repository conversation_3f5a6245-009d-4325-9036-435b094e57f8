"""1.2.1

Revision ID: 69508d1aed24
Revises: 6abeaa9ece15
Create Date: 2023-03-24 11:12:51.646014

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '69508d1aed24'
down_revision = '6abeaa9ece15'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # 1.2.1
    try:
        with op.batch_alter_table("SITE_BRUSH_TASK") as batch_op:
            batch_op.add_column(sa.Column('RSSURL', sa.Text, nullable=True))
    except Exception as e:
        pass
    # ### end Alembic commands ###


def downgrade() -> None:
    pass
