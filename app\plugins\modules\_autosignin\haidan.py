from app.plugins.modules._autosignin._base import _ISiteSigninHandler
from app.utils import SiteUtils, RequestUtils
from config import Config


class HaiDan(_ISiteSigninHandler):
    """
    海胆签到
    """
    # 匹配的站点Url，每一个实现类都需要设置为自己的站点Url
    site_url = "haidan.video"

    # 签到成功
    _succeed_regex = ['(?<=value=")已经打卡(?=")']

    @classmethod
    def match(cls, url):
        """
        根据站点Url判断是否匹配当前站点签到类，大部分情况使用默认实现即可
        :param url: 站点Url
        :return: 是否匹配，如匹配则会调用该类的signin方法
        """
        return True if SiteUtils.url_equal(url, cls.site_url) else False

    def signin(self, site_info: dict):
        """
        执行签到操作
        :param site_info: 站点信息，含有站点Url、站点Cookie、UA等信息
        :return: 签到结果信息
        """
        site = site_info.get("name")
        site_cookie = site_info.get("cookie")
        ua = site_info.get("ua")
        proxy = Config().get_proxies() if site_info.get("proxy") else None

        # 签到
        sign_res = RequestUtils(cookies=site_cookie,
                                ua=ua,
                                proxies=proxy
                                ).get_res(url="https://www.haidan.video/signin.php")
        if not sign_res or sign_res.status_code != 200:
            self.error(f"签到失败，请检查站点连通性")
            return False, f'【{site}】签到失败，请检查站点连通性'

        if "login.php" in sign_res.text:
            self.error(f"签到失败，cookie失效")
            return False, f'【{site}】签到失败，cookie失效'

        sign_status = self.sign_in_result(html_res=sign_res.text,
                                          regexs=self._succeed_regex)
        if sign_status:
            self.info(f"签到成功")
            return True, f'【{site}】签到成功'

        self.error(f"签到失败，签到接口返回 {sign_res.text}")
        return False, f'【{site}】签到失败'
